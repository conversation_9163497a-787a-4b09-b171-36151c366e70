# @ai-sdk/google-vertex

## 2.2.27

### Patch Changes

- aeaf1cd: fix missing systemInstruction in object-tool mode. The object-tool generation mode now properly includes system instructions in API requests, matching the behavior of regular and object-json modes
- Updated dependencies [aeaf1cd]
  - @ai-sdk/google@1.2.22

## 2.2.26

### Patch Changes

- Updated dependencies [ce1625d]
  - @ai-sdk/google@1.2.21

## 2.2.25

### Patch Changes

- Updated dependencies [d532ac3]
- Updated dependencies [08ee85d]
  - @ai-sdk/google@1.2.20

## 2.2.24

### Patch Changes

- Updated dependencies [f262012]
  - @ai-sdk/google@1.2.19

## 2.2.23

### Patch Changes

- Updated dependencies [f64f4f0]
  - @ai-sdk/anthropic@1.2.12

## 2.2.22

### Patch Changes

- fe24216: Add reasoning token output support for gemini models via Vertex AI Provider
- Updated dependencies [4b2e1b0]
  - @ai-sdk/google@1.2.18

## 2.2.21

### Patch Changes

- Updated dependencies [d87b9d1]
  - @ai-sdk/provider-utils@2.2.8
  - @ai-sdk/anthropic@1.2.11
  - @ai-sdk/google@1.2.17

## 2.2.20

### Patch Changes

- Updated dependencies [0ca6f2f]
  - @ai-sdk/google@1.2.16

## 2.2.19

### Patch Changes

- Updated dependencies [2afd354]
  - @ai-sdk/google@1.2.15

## 2.2.18

### Patch Changes

- a85ae99: feat (provider/google-vertex): add imagen-3.0-generate-002
- Updated dependencies [c695a7e]
  - @ai-sdk/google@1.2.14

## 2.2.17

### Patch Changes

- Updated dependencies [6183b08]
  - @ai-sdk/google@1.2.13

## 2.2.16

### Patch Changes

- Updated dependencies [c56331d]
  - @ai-sdk/google@1.2.12

## 2.2.15

### Patch Changes

- Updated dependencies [beef951]
  - @ai-sdk/provider@1.1.3
  - @ai-sdk/anthropic@1.2.10
  - @ai-sdk/google@1.2.11
  - @ai-sdk/provider-utils@2.2.7

## 2.2.14

### Patch Changes

- Updated dependencies [aeba38e]
  - @ai-sdk/anthropic@1.2.9

## 2.2.13

### Patch Changes

- Updated dependencies [013faa8]
  - @ai-sdk/provider@1.1.2
  - @ai-sdk/anthropic@1.2.8
  - @ai-sdk/google@1.2.10
  - @ai-sdk/provider-utils@2.2.6

## 2.2.12

### Patch Changes

- Updated dependencies [c21fa6d]
  - @ai-sdk/provider-utils@2.2.5
  - @ai-sdk/provider@1.1.1
  - @ai-sdk/anthropic@1.2.7
  - @ai-sdk/google@1.2.9

## 2.2.11

### Patch Changes

- Updated dependencies [1e8e66d]
  - @ai-sdk/google@1.2.8

## 2.2.10

### Patch Changes

- 1789884: feat: add provider option schemas for vertex imagegen and google genai
- Updated dependencies [1789884]
  - @ai-sdk/google@1.2.7

## 2.2.9

### Patch Changes

- Updated dependencies [2c19b9a]
  - @ai-sdk/provider-utils@2.2.4
  - @ai-sdk/anthropic@1.2.6
  - @ai-sdk/google@1.2.6

## 2.2.8

### Patch Changes

- 292f543: fix (provider/google-vertex): fix anthropic support for image urls in messages
- Updated dependencies [292f543]
  - @ai-sdk/anthropic@1.2.5

## 2.2.7

### Patch Changes

- Updated dependencies [28be004]
  - @ai-sdk/provider-utils@2.2.3
  - @ai-sdk/anthropic@1.2.4
  - @ai-sdk/google@1.2.5

## 2.2.6

### Patch Changes

- Updated dependencies [b01120e]
  - @ai-sdk/provider-utils@2.2.2
  - @ai-sdk/anthropic@1.2.3
  - @ai-sdk/google@1.2.4

## 2.2.5

### Patch Changes

- 9507f7e: fix (provider/google-vertex): pass through auth options for vertex provider

## 2.2.4

### Patch Changes

- Updated dependencies [aeaa92b]
  - @ai-sdk/anthropic@1.2.2

## 2.2.3

### Patch Changes

- Updated dependencies [871df87]
  - @ai-sdk/google@1.2.3

## 2.2.2

### Patch Changes

- Updated dependencies [f10f0fa]
  - @ai-sdk/provider-utils@2.2.1
  - @ai-sdk/anthropic@1.2.1
  - @ai-sdk/google@1.2.2

## 2.2.1

### Patch Changes

- Updated dependencies [994a13b]
  - @ai-sdk/google@1.2.1

## 2.2.0

### Minor Changes

- 5bc638d: AI SDK 4.2

### Patch Changes

- Updated dependencies [5bc638d]
  - @ai-sdk/anthropic@1.2.0
  - @ai-sdk/google@1.2.0
  - @ai-sdk/provider@1.1.0
  - @ai-sdk/provider-utils@2.2.0

## 2.1.31

### Patch Changes

- Updated dependencies [d0c4659]
  - @ai-sdk/provider-utils@2.1.15
  - @ai-sdk/google@1.1.27
  - @ai-sdk/anthropic@1.1.19

## 2.1.30

### Patch Changes

- Updated dependencies [0bd5bc6]
  - @ai-sdk/provider@1.0.12
  - @ai-sdk/google@1.1.26
  - @ai-sdk/anthropic@1.1.18
  - @ai-sdk/provider-utils@2.1.14

## 2.1.29

### Patch Changes

- Updated dependencies [2e1101a]
  - @ai-sdk/provider@1.0.11
  - @ai-sdk/anthropic@1.1.17
  - @ai-sdk/google@1.1.25
  - @ai-sdk/provider-utils@2.1.13

## 2.1.28

### Patch Changes

- Updated dependencies [5261762]
  - @ai-sdk/google@1.1.24

## 2.1.27

### Patch Changes

- Updated dependencies [413f5a7]
  - @ai-sdk/google@1.1.23

## 2.1.26

### Patch Changes

- Updated dependencies [62f46fd]
  - @ai-sdk/google@1.1.22

## 2.1.25

### Patch Changes

- Updated dependencies [1531959]
  - @ai-sdk/provider-utils@2.1.12
  - @ai-sdk/anthropic@1.1.16
  - @ai-sdk/google@1.1.21

## 2.1.24

### Patch Changes

- Updated dependencies [e1d3d42]
  - @ai-sdk/anthropic@1.1.15
  - @ai-sdk/provider@1.0.10
  - @ai-sdk/google@1.1.20
  - @ai-sdk/provider-utils@2.1.11

## 2.1.23

### Patch Changes

- Updated dependencies [2c27583]
- Updated dependencies [0e8b66c]
  - @ai-sdk/google@1.1.19
  - @ai-sdk/anthropic@1.1.14

## 2.1.22

### Patch Changes

- Updated dependencies [5c8f512]
  - @ai-sdk/google@1.1.18

## 2.1.21

### Patch Changes

- Updated dependencies [3004b14]
  - @ai-sdk/anthropic@1.1.13

## 2.1.20

### Patch Changes

- Updated dependencies [b3e5a15]
  - @ai-sdk/anthropic@1.1.12

## 2.1.19

### Patch Changes

- Updated dependencies [00276ae]
- Updated dependencies [a4f8714]
  - @ai-sdk/anthropic@1.1.11

## 2.1.18

### Patch Changes

- Updated dependencies [ddf9740]
  - @ai-sdk/anthropic@1.1.10
  - @ai-sdk/provider@1.0.9
  - @ai-sdk/google@1.1.17
  - @ai-sdk/provider-utils@2.1.10

## 2.1.17

### Patch Changes

- Updated dependencies [1b2e2a0]
  - @ai-sdk/google@1.1.16

## 2.1.16

### Patch Changes

- Updated dependencies [2761f06]
  - @ai-sdk/provider@1.0.8
  - @ai-sdk/anthropic@1.1.9
  - @ai-sdk/google@1.1.15
  - @ai-sdk/provider-utils@2.1.9

## 2.1.15

### Patch Changes

- Updated dependencies [08a3641]
  - @ai-sdk/google@1.1.14

## 2.1.14

### Patch Changes

- Updated dependencies [2e898b4]
  - @ai-sdk/provider-utils@2.1.8
  - @ai-sdk/anthropic@1.1.8
  - @ai-sdk/google@1.1.13

## 2.1.13

### Patch Changes

- Updated dependencies [3ff4ef8]
  - @ai-sdk/provider-utils@2.1.7
  - @ai-sdk/anthropic@1.1.7
  - @ai-sdk/google@1.1.12

## 2.1.12

### Patch Changes

- Updated dependencies [6eb7fc4]
  - @ai-sdk/google@1.1.11

## 2.1.11

### Patch Changes

- 4da908a: feat (provider/google-vertex): add new gemini models

## 2.1.10

### Patch Changes

- Updated dependencies [e5567f7]
  - @ai-sdk/google@1.1.10

## 2.1.9

### Patch Changes

- Updated dependencies [b2573de]
  - @ai-sdk/google@1.1.9

## 2.1.8

### Patch Changes

- d89c3b9: feat (provider): add image model support to provider specification
- Updated dependencies [d89c3b9]
  - @ai-sdk/provider@1.0.7
  - @ai-sdk/anthropic@1.1.6
  - @ai-sdk/google@1.1.8
  - @ai-sdk/provider-utils@2.1.6

## 2.1.7

### Patch Changes

- d399f25: feat (provider/google-vertex): support public file urls in messages
- Updated dependencies [d399f25]
  - @ai-sdk/google@1.1.7

## 2.1.6

### Patch Changes

- Updated dependencies [e012cd8]
  - @ai-sdk/google@1.1.6

## 2.1.5

### Patch Changes

- Updated dependencies [3a602ca]
  - @ai-sdk/provider-utils@2.1.5
  - @ai-sdk/anthropic@1.1.5
  - @ai-sdk/google@1.1.5

## 2.1.4

### Patch Changes

- Updated dependencies [066206e]
  - @ai-sdk/provider-utils@2.1.4
  - @ai-sdk/anthropic@1.1.4
  - @ai-sdk/google@1.1.4

## 2.1.3

### Patch Changes

- Updated dependencies [39e5c1f]
  - @ai-sdk/provider-utils@2.1.3
  - @ai-sdk/anthropic@1.1.3
  - @ai-sdk/google@1.1.3

## 2.1.2

### Patch Changes

- 3a58a2e: feat (ai/core): throw NoImageGeneratedError from generateImage when no predictions are returned.
- Updated dependencies [ed012d2]
- Updated dependencies [3a58a2e]
  - @ai-sdk/provider-utils@2.1.2
  - @ai-sdk/provider@1.0.6
  - @ai-sdk/anthropic@1.1.2
  - @ai-sdk/google@1.1.2

## 2.1.1

### Patch Changes

- b284e2c: feat (provider/google-vertex): support prompt caching for Anthropic Claude models
- Updated dependencies [e7a9ec9]
- Updated dependencies [858f934]
- Updated dependencies [b284e2c]
- Updated dependencies [0a699f1]
  - @ai-sdk/provider-utils@2.1.1
  - @ai-sdk/anthropic@1.1.1
  - @ai-sdk/provider@1.0.5
  - @ai-sdk/google@1.1.1

## 2.1.0

### Minor Changes

- 62ba5ad: release: AI SDK 4.1

### Patch Changes

- Updated dependencies [62ba5ad]
  - @ai-sdk/anthropic@1.1.0
  - @ai-sdk/google@1.1.0
  - @ai-sdk/provider-utils@2.1.0

## 2.0.19

### Patch Changes

- Updated dependencies [00114c5]
  - @ai-sdk/provider-utils@2.0.8
  - @ai-sdk/anthropic@1.0.9
  - @ai-sdk/google@1.0.17

## 2.0.18

### Patch Changes

- 218d001: feat (provider): Add maxImagesPerCall setting to all image providers.

## 2.0.17

### Patch Changes

- Updated dependencies [4eb9b41]
  - @ai-sdk/google@1.0.16

## 2.0.16

### Patch Changes

- Updated dependencies [7611964]
  - @ai-sdk/google@1.0.15

## 2.0.15

### Patch Changes

- Updated dependencies [90fb95a]
- Updated dependencies [e6dfef4]
- Updated dependencies [6636db6]
  - @ai-sdk/provider-utils@2.0.7
  - @ai-sdk/anthropic@1.0.8
  - @ai-sdk/google@1.0.14

## 2.0.14

### Patch Changes

- 19a2ce7: feat (ai/core): add aspectRatio and seed options to generateImage
- 6337688: feat: change image generation errors to warnings
- Updated dependencies [19a2ce7]
- Updated dependencies [19a2ce7]
- Updated dependencies [6337688]
  - @ai-sdk/provider@1.0.4
  - @ai-sdk/provider-utils@2.0.6
  - @ai-sdk/anthropic@1.0.7
  - @ai-sdk/google@1.0.13

## 2.0.13

### Patch Changes

- e6ed588: feat (provider/google-vertex): Allow arbitrary image model ids.
- 6612561: fix (provider/google-vertex): Use optional fetch in embed and streamline config.

## 2.0.12

### Patch Changes

- 5ed5e45: chore (config): Use ts-library.json tsconfig for no-UI libs.
- Updated dependencies [5ed5e45]
  - @ai-sdk/provider-utils@2.0.5
  - @ai-sdk/anthropic@1.0.6
  - @ai-sdk/provider@1.0.3
  - @ai-sdk/google@1.0.12

## 2.0.11

### Patch Changes

- 5feec50: feat (provider/google-vertex): Add imagen support.

## 2.0.10

### Patch Changes

- d32abbd: feat (provider/google-vertex): Add gemini 2 models.

## 2.0.9

### Patch Changes

- Updated dependencies [db31e74]
  - @ai-sdk/google@1.0.11

## 2.0.8

### Patch Changes

- e07439a: feat (provider/google): Include safety ratings response detail.
- 4017b0f: feat (provider/google-vertex): Enhance grounding metadata response detail.
- a9df182: feat (provider/google): Add support for search grounding.
- Updated dependencies [e07439a]
- Updated dependencies [4017b0f]
- Updated dependencies [a9df182]
  - @ai-sdk/google@1.0.10

## 2.0.7

### Patch Changes

- Updated dependencies [c0b1c7e]
  - @ai-sdk/google@1.0.9

## 2.0.6

### Patch Changes

- b7372dc: feat (provider/google): Include optional response grounding metadata.
- 8224964: feat (provider/google-vertex): Add support for baseURL in API calls.
- Updated dependencies [b7372dc]
  - @ai-sdk/google@1.0.8

## 2.0.5

### Patch Changes

- Updated dependencies [09a9cab]
  - @ai-sdk/provider@1.0.2
  - @ai-sdk/anthropic@1.0.5
  - @ai-sdk/google@1.0.7
  - @ai-sdk/provider-utils@2.0.4

## 2.0.4

### Patch Changes

- 3cfcd0a: fix (provider/google-vertex): Remove unsupported cache control setting from Vertex Anthropic.

## 2.0.3

### Patch Changes

- Updated dependencies [9e54403]
  - @ai-sdk/google@1.0.6

## 2.0.2

### Patch Changes

- 5b0366e: fix (provider/vertex): fix internal reference

## 2.0.1

### Patch Changes

- bcd892e: feat (provider/google-vertex): Add support for Anthropic models.
- Updated dependencies [bcd892e]
  - @ai-sdk/anthropic@1.0.4

## 2.0.0

### Major Changes

- 0984f0b: feat (provider/google-vertex): Rewrite for Edge runtime support.

### Patch Changes

- 0984f0b: chore (providers/google-vertex): Remove unref'd base default provider.
- Updated dependencies [0984f0b]
- Updated dependencies [0984f0b]
  - @ai-sdk/google@1.0.5
  - @ai-sdk/provider-utils@2.0.3

## 1.0.4

### Patch Changes

- 6373c60: fix (provider/google): send json schema into provider

## 1.0.3

### Patch Changes

- Updated dependencies [b446ae5]
  - @ai-sdk/provider@1.0.1
  - @ai-sdk/provider-utils@2.0.2

## 1.0.2

### Patch Changes

- b748dfb: feat (providers): update model lists

## 1.0.1

### Patch Changes

- Updated dependencies [c3ab5de]
  - @ai-sdk/provider-utils@2.0.1

## 1.0.0

### Major Changes

- 66060f7: chore (release): bump major version to 4.0
- 8c5daa3: chore (provider/vertex): remove topK model setting

### Patch Changes

- Updated dependencies [b469a7e]
- Updated dependencies [dce4158]
- Updated dependencies [c0ddc24]
- Updated dependencies [b1da952]
- Updated dependencies [dce4158]
- Updated dependencies [8426f55]
- Updated dependencies [db46ce5]
  - @ai-sdk/provider-utils@2.0.0
  - @ai-sdk/provider@1.0.0

## 1.0.0-canary.3

### Patch Changes

- Updated dependencies [8426f55]
  - @ai-sdk/provider-utils@2.0.0-canary.3

## 1.0.0-canary.2

### Patch Changes

- Updated dependencies [dce4158]
- Updated dependencies [dce4158]
  - @ai-sdk/provider-utils@2.0.0-canary.2

## 1.0.0-canary.1

### Major Changes

- 8c5daa3: chore (provider/vertex): remove topK model setting

### Patch Changes

- Updated dependencies [b1da952]
  - @ai-sdk/provider-utils@2.0.0-canary.1

## 1.0.0-canary.0

### Major Changes

- 66060f7: chore (release): bump major version to 4.0

### Patch Changes

- Updated dependencies [b469a7e]
- Updated dependencies [c0ddc24]
- Updated dependencies [db46ce5]
  - @ai-sdk/provider-utils@2.0.0-canary.0
  - @ai-sdk/provider@1.0.0-canary.0

## 0.0.43

### Patch Changes

- 4360e2d: feat (provider/vertex): expose search grounding metadata
- e7823a3: feat (provider/vertex): add embedding support

## 0.0.42

### Patch Changes

- aa98cdb: chore: more flexible dependency versioning
- 1486128: feat: add supportsUrl to language model specification
- 1486128: feat (provider/google): support native file URLs without download
- 3b1b69a: feat: provider-defined tools
- Updated dependencies [aa98cdb]
- Updated dependencies [1486128]
- Updated dependencies [7b937c5]
- Updated dependencies [3b1b69a]
- Updated dependencies [811a317]
  - @ai-sdk/provider-utils@1.0.22
  - @ai-sdk/provider@0.0.26

## 0.0.41

### Patch Changes

- Updated dependencies [b9b0d7b]
  - @ai-sdk/provider@0.0.25
  - @ai-sdk/provider-utils@1.0.21

## 0.0.40

### Patch Changes

- 8efa1c5: chore (provider/vertex): update GoogleVertexModelId

## 0.0.39

### Patch Changes

- 465189a: feat (provider/vertex): add schema support
- 33ba542: feat (provider/vertex): support frequencyPenalty setting
- 20ffa73: feat (provider/vertex): tool choice support & object generation with tool mode

## 0.0.38

### Patch Changes

- d595d0d: feat (ai/core): file content parts
- Updated dependencies [d595d0d]
  - @ai-sdk/provider@0.0.24
  - @ai-sdk/provider-utils@1.0.20

## 0.0.37

### Patch Changes

- Updated dependencies [273f696]
  - @ai-sdk/provider-utils@1.0.19

## 0.0.36

### Patch Changes

- Updated dependencies [03313cd]
- Updated dependencies [3be7c1c]
  - @ai-sdk/provider-utils@1.0.18
  - @ai-sdk/provider@0.0.23

## 0.0.35

### Patch Changes

- 26515cb: feat (ai/provider): introduce ProviderV1 specification
- Updated dependencies [26515cb]
  - @ai-sdk/provider@0.0.22
  - @ai-sdk/provider-utils@1.0.17

## 0.0.34

### Patch Changes

- Updated dependencies [09f895f]
  - @ai-sdk/provider-utils@1.0.16

## 0.0.33

### Patch Changes

- Updated dependencies [d67fa9c]
  - @ai-sdk/provider-utils@1.0.15

## 0.0.32

### Patch Changes

- Updated dependencies [f2c025e]
  - @ai-sdk/provider@0.0.21
  - @ai-sdk/provider-utils@1.0.14

## 0.0.31

### Patch Changes

- 04af64f: fix (provider/google-vertex): fix broken tool calling

## 0.0.30

### Patch Changes

- Updated dependencies [6ac355e]
  - @ai-sdk/provider@0.0.20
  - @ai-sdk/provider-utils@1.0.13

## 0.0.29

### Patch Changes

- Updated dependencies [dd712ac]
  - @ai-sdk/provider-utils@1.0.12

## 0.0.28

### Patch Changes

- 89b18ca: fix (ai/provider): send finish reason 'unknown' by default
- Updated dependencies [dd4a0f5]
  - @ai-sdk/provider@0.0.19
  - @ai-sdk/provider-utils@1.0.11

## 0.0.27

### Patch Changes

- 48f618d: feat (provider/google): add search grounding support

## 0.0.26

### Patch Changes

- Updated dependencies [4bd27a9]
- Updated dependencies [845754b]
  - @ai-sdk/provider-utils@1.0.10
  - @ai-sdk/provider@0.0.18

## 0.0.25

### Patch Changes

- 1e94ed8: feat (provider/google-vertex): support json mode object generation

## 0.0.24

### Patch Changes

- 39b827a: feat (provider/google-vertex): support json mode object generation

## 0.0.23

### Patch Changes

- Updated dependencies [029af4c]
  - @ai-sdk/provider@0.0.17
  - @ai-sdk/provider-utils@1.0.9

## 0.0.22

### Patch Changes

- Updated dependencies [d58517b]
  - @ai-sdk/provider@0.0.16
  - @ai-sdk/provider-utils@1.0.8

## 0.0.21

### Patch Changes

- Updated dependencies [96aed25]
  - @ai-sdk/provider@0.0.15
  - @ai-sdk/provider-utils@1.0.7

## 0.0.20

### Patch Changes

- Updated dependencies [9614584]
- Updated dependencies [0762a22]
  - @ai-sdk/provider-utils@1.0.6

## 0.0.19

### Patch Changes

- a8d1c9e9: feat (ai/core): parallel image download
- Updated dependencies [a8d1c9e9]
  - @ai-sdk/provider-utils@1.0.5
  - @ai-sdk/provider@0.0.14

## 0.0.18

### Patch Changes

- Updated dependencies [4f88248f]
  - @ai-sdk/provider-utils@1.0.4

## 0.0.17

### Patch Changes

- 2b9da0f0: feat (core): support stopSequences setting.
- a5b58845: feat (core): support topK setting
- 4aa8deb3: feat (provider): support responseFormat setting in provider api
- 13b27ec6: chore (ai/core): remove grammar mode
- Updated dependencies [2b9da0f0]
- Updated dependencies [a5b58845]
- Updated dependencies [4aa8deb3]
- Updated dependencies [13b27ec6]
  - @ai-sdk/provider@0.0.13
  - @ai-sdk/provider-utils@1.0.3

## 0.0.16

### Patch Changes

- 0eabc798: feat (provider/google-vertex): change vertexai library into peer dependency

## 0.0.15

### Patch Changes

- bb584330: feat (provider/google-vertex): use systemInstruction content parts

## 0.0.14

### Patch Changes

- Updated dependencies [b7290943]
  - @ai-sdk/provider@0.0.12
  - @ai-sdk/provider-utils@1.0.2

## 0.0.13

### Patch Changes

- Updated dependencies [d481729f]
  - @ai-sdk/provider-utils@1.0.1

## 0.0.12

### Patch Changes

- 5edc6110: feat (ai/core): add custom request header support
- Updated dependencies [5edc6110]
- Updated dependencies [5edc6110]
- Updated dependencies [5edc6110]
  - @ai-sdk/provider@0.0.11
  - @ai-sdk/provider-utils@1.0.0

## 0.0.11

### Patch Changes

- Updated dependencies [02f6a088]
  - @ai-sdk/provider-utils@0.0.16

## 0.0.10

### Patch Changes

- Updated dependencies [85712895]
- Updated dependencies [85712895]
  - @ai-sdk/provider-utils@0.0.15

## 0.0.9

### Patch Changes

- 4728c37f: feat (core): add text embedding model support to provider registry
- Updated dependencies [7910ae84]
  - @ai-sdk/provider-utils@0.0.14

## 0.0.8

### Patch Changes

- Updated dependencies [102ca22f]
  - @ai-sdk/provider@0.0.10
  - @ai-sdk/provider-utils@0.0.13

## 0.0.7

### Patch Changes

- 09295e2e: feat (@ai-sdk/google-vertex): automatically download image URLs
- Updated dependencies [09295e2e]
- Updated dependencies [09295e2e]
- Updated dependencies [043a5de2]
  - @ai-sdk/provider@0.0.9
  - @ai-sdk/provider-utils@0.0.12

## 0.0.6

### Patch Changes

- 3a7a4ab6: fix (provider/vertex): fix undefined parts handling

## 0.0.5

### Patch Changes

- 7cab5e9c: feat (provider/vertex): add safety setting option on models

## 0.0.4

### Patch Changes

- f727d197: fix (provider/vertex): correct assistant message conversion
- f727d197: feat (provider/vertex): add tool call support
- 94c60cd3: feat (provider/google): add googleAuthOptions provider configuration setting

## 0.0.3

### Patch Changes

- f39c0dd2: feat (provider): implement toolChoice support
- Updated dependencies [f39c0dd2]
  - @ai-sdk/provider@0.0.8
  - @ai-sdk/provider-utils@0.0.11

## 0.0.2

### Patch Changes

- 24683b72: fix (provider/google-vertex): zod is not a dependency
- Updated dependencies [8e780288]
  - @ai-sdk/provider@0.0.7
  - @ai-sdk/provider-utils@0.0.10

## 0.0.1

### Patch Changes

- 6a50ac4: feat (provider/google-vertex): add Google Vertex provider (text generation and streaming only)
- Updated dependencies [6a50ac4]
- Updated dependencies [6a50ac4]
  - @ai-sdk/provider@0.0.6
  - @ai-sdk/provider-utils@0.0.9
