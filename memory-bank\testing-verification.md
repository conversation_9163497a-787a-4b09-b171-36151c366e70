# Testing & Verification Guide

## Overview

This guide provides comprehensive testing procedures to verify the dual MCP setup works correctly without conflicts.

## Pre-Implementation Testing

### Baseline Verification

#### Test 1: Identify Current Conflict

**Purpose**: Confirm the conflict exists before modifications

**Steps**:
1. Open Cursor with a project
2. Use TaskMaster: "Show me the current tasks"
3. From WSL terminal in same project, launch Claude Code
4. Use TaskMaster: "Initialize taskmaster-ai in my project"
5. Return to Cursor and use TaskMaster again

**Expected Result**: Configuration conflicts, inconsistent behavior

**Document**: Record specific error messages and behaviors

#### Test 2: Configuration Path Detection

**Purpose**: Verify where each MCP looks for configuration

**Test Script**:
```bash
#!/bin/bash
echo "=== Pre-Implementation Configuration Paths ==="

# Test Windows MCP path detection
echo "Windows MCP would use:"
# Run from project directory to see what findProjectRoot() returns

# Test WSL MCP path detection  
echo "WSL MCP would use:"
cd /some/test/project
# Check where WSL MCP looks for config
```

## Post-Implementation Testing

### Phase 1: Isolation Verification

#### Test 1: Windows MCP Isolation

**Purpose**: Verify Windows MCP uses installation directory only

**Steps**:
1. Open Cursor
2. Navigate to test project: `C:\Users\<USER>\TestProject\`
3. Create simple test project structure:
   ```
   TestProject/
   ├── .git/
   ├── package.json
   └── src/
   ```
4. Use TaskMaster command: "Show me the current configuration"
5. Use TaskMaster command: "Initialize taskmaster-ai in my project"

**Expected Results**:
- ✅ Configuration location: `C:\Users\<USER>\Documents\Cline\MCP\task-master-ai\.taskmaster\config.json`
- ✅ **NO** `.taskmaster/` directory created in `C:\Users\<USER>\TestProject\`
- ✅ TaskMaster operates normally
- ✅ Configuration shows "Windows TaskMaster" project name

**Verification Commands**:
```bash
# Check Windows MCP config location
ls -la "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster/"

# Verify NO project modification
ls -la "/mnt/c/Users/<USER>/TestProject/" | grep taskmaster
# Should return nothing
```

#### Test 2: WSL MCP Project Integration

**Purpose**: Verify WSL MCP uses project-based configuration

**Steps**:
1. Open WSL terminal
2. Navigate to test project: `/mnt/c/Users/<USER>/TestProject/`
3. Launch Claude Code from this directory
4. Use TaskMaster command: "Initialize taskmaster-ai in my project" 
5. Use TaskMaster command: "Show me the current configuration"

**Expected Results**:
- ✅ `.taskmaster/` directory created in `/mnt/c/Users/<USER>/TestProject/`
- ✅ Configuration location: `/mnt/c/Users/<USER>/TestProject/.taskmaster/config.json`
- ✅ TaskMaster operates normally
- ✅ Full project integration

**Verification Commands**:
```bash
# Check project-based config creation
ls -la "/mnt/c/Users/<USER>/TestProject/.taskmaster/"

# Verify config contents
cat "/mnt/c/Users/<USER>/TestProject/.taskmaster/config.json"
```

#### Test 3: Simultaneous Operation

**Purpose**: Verify both MCPs can operate on same project without conflicts

**Setup**:
1. Use the same test project: `C:\Users\<USER>\TestProject\`
2. Ensure both MCPs are connected and operational

**Test Sequence**:

**Windows Cursor (Terminal 1)**:
```
1. Open Cursor
2. Open TestProject
3. Chat: "Add a new task: Implement user authentication"
4. Chat: "Show me all current tasks"
5. Chat: "Update task 1 status to in-progress"
```

**WSL Claude Code (Terminal 2)**:
```
1. cd /mnt/c/Users/<USER>/TestProject
2. claude-code
3. Chat: "Add a new task: Setup database schema"  
4. Chat: "Show me all current tasks"
5. Chat: "Generate task files for all tasks"
```

**Expected Results**:
- ✅ Windows MCP maintains separate task list in installation directory
- ✅ WSL MCP maintains separate task list in project directory
- ✅ No cross-contamination between task databases
- ✅ Both operate smoothly without errors

### Phase 2: Configuration Verification

#### Test 4: Environment Variable Verification

**Purpose**: Verify TASKMASTER_CONFIG_ROOT is working

**Test Script**:
```bash
#!/bin/bash
echo "=== Environment Variable Test ==="

# Check if Windows MCP server sets the environment variable
cd "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/mcp-server"
node -e "
import('./server.js').then(() => {
  console.log('TASKMASTER_CONFIG_ROOT:', process.env.TASKMASTER_CONFIG_ROOT);
});
"
```

**Expected Output**:
```
TASKMASTER_CONFIG_ROOT: C:\Users\<USER>\Documents\Cline\MCP\task-master-ai
```

#### Test 5: Configuration Loading Verification

**Purpose**: Verify each MCP loads correct configuration

**Windows MCP Test**:
```javascript
// Test in Cursor chat
"Show me the current models configuration and tell me the project name from global settings"
```

**Expected Response**:
- Project name: "Windows TaskMaster" 
- Config source should reference installation directory

**WSL MCP Test**:
```javascript  
// Test in Claude Code chat
"Show me the current models configuration and tell me the project name from global settings"
```

**Expected Response**:
- Project name: Should be project-specific or default
- Config source should reference project directory

### Phase 3: Stress Testing

#### Test 6: Rapid Operation Test

**Purpose**: Verify stability under rapid operations

**Test Script**:
```bash
#!/bin/bash
echo "=== Rapid Operation Test ==="

# Windows MCP rapid operations (run in Cursor)
for i in {1..10}; do
  echo "Windows MCP - Adding task $i"
  # Use Cursor chat: "Add task: Test task $i"
  sleep 1
done

# WSL MCP rapid operations (run in Claude Code)  
for i in {1..10}; do
  echo "WSL MCP - Adding task $i"
  # Use Claude Code chat: "Add task: WSL test task $i"
  sleep 1
done
```

**Expected Results**:
- ✅ Both MCPs handle rapid operations without errors
- ✅ No configuration file lock conflicts
- ✅ All tasks are properly saved
- ✅ No data corruption

#### Test 7: Configuration Modification Test

**Purpose**: Verify configuration changes don't interfere

**Steps**:
1. **Windows MCP**: Change main model to different provider
   ```
   "Change the main model to openai/gpt-4"
   ```

2. **WSL MCP**: Change main model to different provider
   ```
   "Change the main model to claude-code/sonnet"  
   ```

3. **Verification**: Check both configurations are independent
   ```bash
   # Check Windows config
   cat "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster/config.json"
   
   # Check WSL config (in project)
   cat "/mnt/c/Users/<USER>/TestProject/.taskmaster/config.json"
   ```

**Expected Results**:
- ✅ Windows MCP config shows OpenAI GPT-4
- ✅ WSL MCP config shows Claude Code Sonnet
- ✅ Changes are isolated and persistent

### Phase 4: Edge Case Testing

#### Test 8: Missing Configuration Directory

**Purpose**: Verify behavior when config directories don't exist

**Setup**:
```bash
# Remove Windows MCP config directory
rm -rf "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster"
```

**Test**:
1. Restart Cursor
2. Use TaskMaster: "Show me the current configuration"

**Expected Results**:
- ✅ Windows MCP creates new `.taskmaster` directory in installation path
- ✅ Uses default configuration
- ✅ No errors or crashes

#### Test 9: Permission Issues Test

**Purpose**: Verify behavior with permission restrictions

**Setup**:
```bash
# Make config directory read-only
chmod -w "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster"
```

**Test**:
1. Use Windows MCP: "Change the main model to anthropic/claude-3-5-sonnet"

**Expected Results**:
- ✅ Appropriate error message about write permissions
- ✅ No crashes or undefined behavior
- ✅ System remains stable

**Cleanup**:
```bash
# Restore permissions
chmod +w "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster"
```

#### Test 10: Cross-Platform Path Test

**Purpose**: Verify Windows/WSL path handling

**Test**:
1. Create project with spaces in name: `C:\Users\<USER>\Test Project\`
2. Test both MCPs with this project
3. Verify path resolution works correctly

**Expected Results**:
- ✅ Both MCPs handle spaces in paths correctly
- ✅ No path resolution errors
- ✅ Configuration files created in correct locations

## Automated Testing Suite

### Test Automation Script

**File**: `test-dual-mcp.sh`

```bash
#!/bin/bash

# Dual MCP Testing Suite
set -e

echo "🧪 TaskMaster Dual MCP Testing Suite"
echo "=================================="

# Test 1: Environment Setup
echo "📋 Test 1: Environment Setup"
if [ -d "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster" ]; then
    echo "✅ Windows MCP config directory exists"
else
    echo "❌ Windows MCP config directory missing"
    exit 1
fi

# Test 2: WSL MCP Functionality  
echo "📋 Test 2: WSL MCP Project Detection"
cd /tmp
mkdir -p test-project-$$
cd test-project-$$
git init
echo '{}' > package.json

# Simulate WSL MCP behavior
if /home/<USER>/projects/mcp-servers/claude-task-master-main/mcp-server/server.js --test-mode 2>/dev/null; then
    echo "✅ WSL MCP operational"
else
    echo "❌ WSL MCP issues detected"
fi

# Test 3: Configuration Isolation
echo "📋 Test 3: Configuration Isolation"
WINDOWS_CONFIG="/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster/config.json"
PROJECT_CONFIG="/tmp/test-project-$$/.taskmaster/config.json"

if [ -f "$WINDOWS_CONFIG" ] && [ ! -f "$PROJECT_CONFIG" ]; then
    echo "✅ Configuration isolation verified"
else
    echo "❌ Configuration isolation failed"
fi

# Cleanup
cd /tmp
rm -rf test-project-$$

echo "=================================="
echo "🎉 Testing completed"
```

### Continuous Monitoring

**File**: `monitor-dual-mcp.sh`

```bash
#!/bin/bash

# Continuous monitoring script
while true; do
    echo "$(date): Checking MCP health..."
    
    # Check Windows MCP config access
    if [ -r "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster/config.json" ]; then
        echo "✅ Windows MCP config accessible"
    else
        echo "❌ Windows MCP config issues"
    fi
    
    # Check for TaskMaster processes
    PROCESS_COUNT=$(ps aux | grep -c "task-master" | grep -v grep)
    echo "📊 TaskMaster processes running: $PROCESS_COUNT"
    
    # Check memory usage
    MEMORY_USAGE=$(ps aux | grep "task-master" | grep -v grep | awk '{sum+=$6} END {print sum}')
    echo "💾 Total memory usage: ${MEMORY_USAGE}KB"
    
    sleep 60
done
```

## Success Criteria

### Must Pass Tests
- ✅ Test 1: Windows MCP Isolation
- ✅ Test 2: WSL MCP Project Integration  
- ✅ Test 3: Simultaneous Operation
- ✅ Test 4: Environment Variable Verification
- ✅ Test 5: Configuration Loading Verification

### Should Pass Tests
- ✅ Test 6: Rapid Operation Test
- ✅ Test 7: Configuration Modification Test
- ✅ Test 8: Missing Configuration Directory
- ✅ Test 9: Permission Issues Test
- ✅ Test 10: Cross-Platform Path Test

## Troubleshooting Failed Tests

### Common Failures

**Windows MCP Still Uses Project Directory**:
- Check server.js modifications
- Verify TASKMASTER_CONFIG_ROOT environment variable
- Check config-manager.js modifications

**WSL MCP Not Working**:
- Verify WSL installation unchanged
- Check file permissions
- Verify Claude Code configuration

**Both MCPs Conflict**:
- Check if modifications were applied correctly
- Verify environment variable is set
- Check for cached configurations

### Debug Commands

```bash
# Check current configuration paths
find /mnt/c -name "config.json" | grep taskmaster

# Check environment variables
env | grep TASKMASTER

# Check running processes
ps aux | grep task-master

# Check file permissions
ls -la "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster/"
```

## Reporting

### Test Report Template

```
# TaskMaster Dual MCP Test Report

## Test Environment
- Date: [DATE]
- Windows MCP Version: [VERSION]
- WSL MCP Version: [VERSION]
- Test Suite Version: [VERSION]

## Test Results
- [✅/❌] Windows MCP Isolation
- [✅/❌] WSL MCP Project Integration
- [✅/❌] Simultaneous Operation
- [✅/❌] Environment Variable Verification
- [✅/❌] Configuration Loading Verification

## Performance Metrics
- Memory Usage: [AMOUNT]
- Response Times: [TIMES]
- Error Rate: [PERCENTAGE]

## Issues Found
[LIST ANY ISSUES]

## Recommendations
[LIST RECOMMENDATIONS]
```

This comprehensive testing guide ensures the dual MCP setup works correctly and maintains isolation between Windows and WSL instances.