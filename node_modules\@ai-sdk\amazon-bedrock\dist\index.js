"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var src_exports = {};
__export(src_exports, {
  bedrock: () => bedrock,
  createAmazonBedrock: () => createAmazonBedrock
});
module.exports = __toCommonJS(src_exports);

// src/bedrock-provider.ts
var import_provider_utils7 = require("@ai-sdk/provider-utils");

// src/bedrock-chat-language-model.ts
var import_provider4 = require("@ai-sdk/provider");
var import_provider_utils3 = require("@ai-sdk/provider-utils");
var import_zod2 = require("zod");

// src/bedrock-api-types.ts
var BEDROCK_CACHE_POINT = {
  cachePoint: { type: "default" }
};
var BEDROCK_STOP_REASONS = [
  "stop",
  "stop_sequence",
  "end_turn",
  "length",
  "max_tokens",
  "content-filter",
  "content_filtered",
  "guardrail_intervened",
  "tool-calls",
  "tool_use"
];

// src/bedrock-error.ts
var import_zod = require("zod");
var BedrockErrorSchema = import_zod.z.object({
  message: import_zod.z.string(),
  type: import_zod.z.string().nullish()
});

// src/bedrock-event-stream-response-handler.ts
var import_provider = require("@ai-sdk/provider");
var import_provider_utils = require("@ai-sdk/provider-utils");
var import_eventstream_codec = require("@smithy/eventstream-codec");
var import_util_utf8 = require("@smithy/util-utf8");
var createBedrockEventStreamResponseHandler = (chunkSchema) => async ({ response }) => {
  const responseHeaders = (0, import_provider_utils.extractResponseHeaders)(response);
  if (response.body == null) {
    throw new import_provider.EmptyResponseBodyError({});
  }
  const codec = new import_eventstream_codec.EventStreamCodec(import_util_utf8.toUtf8, import_util_utf8.fromUtf8);
  let buffer = new Uint8Array(0);
  const textDecoder = new TextDecoder();
  return {
    responseHeaders,
    value: response.body.pipeThrough(
      new TransformStream({
        transform(chunk, controller) {
          var _a, _b;
          const newBuffer = new Uint8Array(buffer.length + chunk.length);
          newBuffer.set(buffer);
          newBuffer.set(chunk, buffer.length);
          buffer = newBuffer;
          while (buffer.length >= 4) {
            const totalLength = new DataView(
              buffer.buffer,
              buffer.byteOffset,
              buffer.byteLength
            ).getUint32(0, false);
            if (buffer.length < totalLength) {
              break;
            }
            try {
              const subView = buffer.subarray(0, totalLength);
              const decoded = codec.decode(subView);
              buffer = buffer.slice(totalLength);
              if (((_a = decoded.headers[":message-type"]) == null ? void 0 : _a.value) === "event") {
                const data = textDecoder.decode(decoded.body);
                const parsedDataResult = (0, import_provider_utils.safeParseJSON)({ text: data });
                if (!parsedDataResult.success) {
                  controller.enqueue(parsedDataResult);
                  break;
                }
                delete parsedDataResult.value.p;
                let wrappedData = {
                  [(_b = decoded.headers[":event-type"]) == null ? void 0 : _b.value]: parsedDataResult.value
                };
                const validatedWrappedData = (0, import_provider_utils.safeValidateTypes)({
                  value: wrappedData,
                  schema: chunkSchema
                });
                if (!validatedWrappedData.success) {
                  controller.enqueue(validatedWrappedData);
                } else {
                  controller.enqueue({
                    success: true,
                    value: validatedWrappedData.value,
                    rawValue: wrappedData
                  });
                }
              }
            } catch (e) {
              break;
            }
          }
        }
      })
    )
  };
};

// src/bedrock-prepare-tools.ts
var import_provider2 = require("@ai-sdk/provider");
function prepareTools(mode) {
  var _a;
  const tools = ((_a = mode.tools) == null ? void 0 : _a.length) ? mode.tools : void 0;
  if (tools == null) {
    return {
      toolConfig: { tools: void 0, toolChoice: void 0 },
      toolWarnings: []
    };
  }
  const toolWarnings = [];
  const bedrockTools = [];
  for (const tool of tools) {
    if (tool.type === "provider-defined") {
      toolWarnings.push({ type: "unsupported-tool", tool });
    } else {
      bedrockTools.push({
        toolSpec: {
          name: tool.name,
          description: tool.description,
          inputSchema: {
            json: tool.parameters
          }
        }
      });
    }
  }
  const toolChoice = mode.toolChoice;
  if (toolChoice == null) {
    return {
      toolConfig: { tools: bedrockTools, toolChoice: void 0 },
      toolWarnings
    };
  }
  const type = toolChoice.type;
  switch (type) {
    case "auto":
      return {
        toolConfig: { tools: bedrockTools, toolChoice: { auto: {} } },
        toolWarnings
      };
    case "required":
      return {
        toolConfig: { tools: bedrockTools, toolChoice: { any: {} } },
        toolWarnings
      };
    case "none":
      return {
        toolConfig: { tools: void 0, toolChoice: void 0 },
        toolWarnings
      };
    case "tool":
      return {
        toolConfig: {
          tools: bedrockTools,
          toolChoice: { tool: { name: toolChoice.toolName } }
        },
        toolWarnings
      };
    default: {
      const _exhaustiveCheck = type;
      throw new import_provider2.UnsupportedFunctionalityError({
        functionality: `Unsupported tool choice type: ${_exhaustiveCheck}`
      });
    }
  }
}

// src/convert-to-bedrock-chat-messages.ts
var import_provider3 = require("@ai-sdk/provider");
var import_provider_utils2 = require("@ai-sdk/provider-utils");
var generateFileId = (0, import_provider_utils2.createIdGenerator)({ prefix: "file", size: 16 });
function getCachePoint(providerMetadata) {
  var _a;
  return (_a = providerMetadata == null ? void 0 : providerMetadata.bedrock) == null ? void 0 : _a.cachePoint;
}
function convertToBedrockChatMessages(prompt) {
  var _a, _b, _c, _d, _e;
  const blocks = groupIntoBlocks(prompt);
  let system = [];
  const messages = [];
  for (let i = 0; i < blocks.length; i++) {
    const block = blocks[i];
    const isLastBlock = i === blocks.length - 1;
    const type = block.type;
    switch (type) {
      case "system": {
        if (messages.length > 0) {
          throw new import_provider3.UnsupportedFunctionalityError({
            functionality: "Multiple system messages that are separated by user/assistant messages"
          });
        }
        for (const message of block.messages) {
          system.push({ text: message.content });
          if (getCachePoint(message.providerMetadata)) {
            system.push(BEDROCK_CACHE_POINT);
          }
        }
        break;
      }
      case "user": {
        const bedrockContent = [];
        for (const message of block.messages) {
          const { role, content, providerMetadata } = message;
          switch (role) {
            case "user": {
              for (let j = 0; j < content.length; j++) {
                const part = content[j];
                switch (part.type) {
                  case "text": {
                    bedrockContent.push({
                      text: part.text
                    });
                    break;
                  }
                  case "image": {
                    if (part.image instanceof URL) {
                      throw new import_provider3.UnsupportedFunctionalityError({
                        functionality: "Image URLs in user messages"
                      });
                    }
                    bedrockContent.push({
                      image: {
                        format: (_b = (_a = part.mimeType) == null ? void 0 : _a.split(
                          "/"
                        )) == null ? void 0 : _b[1],
                        source: {
                          bytes: (0, import_provider_utils2.convertUint8ArrayToBase64)(
                            (_c = part.image) != null ? _c : part.image
                          )
                        }
                      }
                    });
                    break;
                  }
                  case "file": {
                    if (part.data instanceof URL) {
                      throw new import_provider3.UnsupportedFunctionalityError({
                        functionality: "File URLs in user messages"
                      });
                    }
                    bedrockContent.push({
                      document: {
                        format: (_e = (_d = part.mimeType) == null ? void 0 : _d.split(
                          "/"
                        )) == null ? void 0 : _e[1],
                        name: generateFileId(),
                        source: {
                          bytes: part.data
                        }
                      }
                    });
                    break;
                  }
                }
              }
              break;
            }
            case "tool": {
              for (let i2 = 0; i2 < content.length; i2++) {
                const part = content[i2];
                const toolResultContent = part.content != void 0 ? part.content.map((part2) => {
                  switch (part2.type) {
                    case "text":
                      return {
                        text: part2.text
                      };
                    case "image":
                      if (!part2.mimeType) {
                        throw new Error(
                          "Image mime type is required in tool result part content"
                        );
                      }
                      const format = part2.mimeType.split("/")[1];
                      if (!isBedrockImageFormat(format)) {
                        throw new Error(
                          `Unsupported image format: ${format}`
                        );
                      }
                      return {
                        image: {
                          format,
                          source: {
                            bytes: part2.data
                          }
                        }
                      };
                  }
                }) : [{ text: JSON.stringify(part.result) }];
                bedrockContent.push({
                  toolResult: {
                    toolUseId: part.toolCallId,
                    content: toolResultContent
                  }
                });
              }
              break;
            }
            default: {
              const _exhaustiveCheck = role;
              throw new Error(`Unsupported role: ${_exhaustiveCheck}`);
            }
          }
          if (getCachePoint(providerMetadata)) {
            bedrockContent.push(BEDROCK_CACHE_POINT);
          }
        }
        messages.push({ role: "user", content: bedrockContent });
        break;
      }
      case "assistant": {
        const bedrockContent = [];
        for (let j = 0; j < block.messages.length; j++) {
          const message = block.messages[j];
          const isLastMessage = j === block.messages.length - 1;
          const { content } = message;
          for (let k = 0; k < content.length; k++) {
            const part = content[k];
            const isLastContentPart = k === content.length - 1;
            switch (part.type) {
              case "text": {
                bedrockContent.push({
                  text: (
                    // trim the last text part if it's the last message in the block
                    // because Bedrock does not allow trailing whitespace
                    // in pre-filled assistant responses
                    trimIfLast(
                      isLastBlock,
                      isLastMessage,
                      isLastContentPart,
                      part.text
                    )
                  )
                });
                break;
              }
              case "reasoning": {
                bedrockContent.push({
                  reasoningContent: {
                    reasoningText: {
                      // trim the last text part if it's the last message in the block
                      // because Bedrock does not allow trailing whitespace
                      // in pre-filled assistant responses
                      text: trimIfLast(
                        isLastBlock,
                        isLastMessage,
                        isLastContentPart,
                        part.text
                      ),
                      signature: part.signature
                    }
                  }
                });
                break;
              }
              case "redacted-reasoning": {
                bedrockContent.push({
                  reasoningContent: {
                    redactedReasoning: {
                      data: part.data
                    }
                  }
                });
                break;
              }
              case "tool-call": {
                bedrockContent.push({
                  toolUse: {
                    toolUseId: part.toolCallId,
                    name: part.toolName,
                    input: part.args
                  }
                });
                break;
              }
            }
          }
          if (getCachePoint(message.providerMetadata)) {
            bedrockContent.push(BEDROCK_CACHE_POINT);
          }
        }
        messages.push({ role: "assistant", content: bedrockContent });
        break;
      }
      default: {
        const _exhaustiveCheck = type;
        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);
      }
    }
  }
  return { system, messages };
}
function isBedrockImageFormat(format) {
  return ["jpeg", "png", "gif"].includes(format);
}
function trimIfLast(isLastBlock, isLastMessage, isLastContentPart, text) {
  return isLastBlock && isLastMessage && isLastContentPart ? text.trim() : text;
}
function groupIntoBlocks(prompt) {
  const blocks = [];
  let currentBlock = void 0;
  for (const message of prompt) {
    const { role } = message;
    switch (role) {
      case "system": {
        if ((currentBlock == null ? void 0 : currentBlock.type) !== "system") {
          currentBlock = { type: "system", messages: [] };
          blocks.push(currentBlock);
        }
        currentBlock.messages.push(message);
        break;
      }
      case "assistant": {
        if ((currentBlock == null ? void 0 : currentBlock.type) !== "assistant") {
          currentBlock = { type: "assistant", messages: [] };
          blocks.push(currentBlock);
        }
        currentBlock.messages.push(message);
        break;
      }
      case "user": {
        if ((currentBlock == null ? void 0 : currentBlock.type) !== "user") {
          currentBlock = { type: "user", messages: [] };
          blocks.push(currentBlock);
        }
        currentBlock.messages.push(message);
        break;
      }
      case "tool": {
        if ((currentBlock == null ? void 0 : currentBlock.type) !== "user") {
          currentBlock = { type: "user", messages: [] };
          blocks.push(currentBlock);
        }
        currentBlock.messages.push(message);
        break;
      }
      default: {
        const _exhaustiveCheck = role;
        throw new Error(`Unsupported role: ${_exhaustiveCheck}`);
      }
    }
  }
  return blocks;
}

// src/map-bedrock-finish-reason.ts
function mapBedrockFinishReason(finishReason) {
  switch (finishReason) {
    case "stop_sequence":
    case "end_turn":
      return "stop";
    case "max_tokens":
      return "length";
    case "content_filtered":
    case "guardrail_intervened":
      return "content-filter";
    case "tool_use":
      return "tool-calls";
    default:
      return "unknown";
  }
}

// src/bedrock-chat-language-model.ts
var BedrockChatLanguageModel = class {
  constructor(modelId, settings, config) {
    this.modelId = modelId;
    this.settings = settings;
    this.config = config;
    this.specificationVersion = "v1";
    this.provider = "amazon-bedrock";
    this.defaultObjectGenerationMode = "tool";
    this.supportsImageUrls = false;
  }
  getArgs({
    mode,
    prompt,
    maxTokens,
    temperature,
    topP,
    topK,
    frequencyPenalty,
    presencePenalty,
    stopSequences,
    responseFormat,
    seed,
    providerMetadata
  }) {
    var _a, _b, _c, _d, _e, _f, _g;
    const type = mode.type;
    const warnings = [];
    if (frequencyPenalty != null) {
      warnings.push({
        type: "unsupported-setting",
        setting: "frequencyPenalty"
      });
    }
    if (presencePenalty != null) {
      warnings.push({
        type: "unsupported-setting",
        setting: "presencePenalty"
      });
    }
    if (seed != null) {
      warnings.push({
        type: "unsupported-setting",
        setting: "seed"
      });
    }
    if (topK != null) {
      warnings.push({
        type: "unsupported-setting",
        setting: "topK"
      });
    }
    if (responseFormat != null && responseFormat.type !== "text") {
      warnings.push({
        type: "unsupported-setting",
        setting: "responseFormat",
        details: "JSON response format is not supported."
      });
    }
    const { system, messages } = convertToBedrockChatMessages(prompt);
    const reasoningConfigOptions = BedrockReasoningConfigOptionsSchema.safeParse(
      (_a = providerMetadata == null ? void 0 : providerMetadata.bedrock) == null ? void 0 : _a.reasoningConfig
    );
    if (!reasoningConfigOptions.success) {
      throw new import_provider4.InvalidArgumentError({
        argument: "providerOptions.bedrock.reasoningConfig",
        message: "invalid reasoning configuration options",
        cause: reasoningConfigOptions.error
      });
    }
    const isThinking = ((_b = reasoningConfigOptions.data) == null ? void 0 : _b.type) === "enabled";
    const thinkingBudget = (_e = (_c = reasoningConfigOptions.data) == null ? void 0 : _c.budgetTokens) != null ? _e : (_d = reasoningConfigOptions.data) == null ? void 0 : _d.budget_tokens;
    const inferenceConfig = {
      ...maxTokens != null && { maxTokens },
      ...temperature != null && { temperature },
      ...topP != null && { topP },
      ...stopSequences != null && { stopSequences }
    };
    if (isThinking && thinkingBudget != null) {
      if (inferenceConfig.maxTokens != null) {
        inferenceConfig.maxTokens += thinkingBudget;
      } else {
        inferenceConfig.maxTokens = thinkingBudget + 4096;
      }
      this.settings.additionalModelRequestFields = {
        ...this.settings.additionalModelRequestFields,
        reasoningConfig: {
          type: (_f = reasoningConfigOptions.data) == null ? void 0 : _f.type,
          budget_tokens: thinkingBudget
        }
      };
    }
    if (isThinking && inferenceConfig.temperature != null) {
      delete inferenceConfig.temperature;
      warnings.push({
        type: "unsupported-setting",
        setting: "temperature",
        details: "temperature is not supported when thinking is enabled"
      });
    }
    if (isThinking && inferenceConfig.topP != null) {
      delete inferenceConfig.topP;
      warnings.push({
        type: "unsupported-setting",
        setting: "topP",
        details: "topP is not supported when thinking is enabled"
      });
    }
    const baseArgs = {
      system,
      additionalModelRequestFields: this.settings.additionalModelRequestFields,
      ...Object.keys(inferenceConfig).length > 0 && {
        inferenceConfig
      },
      messages,
      ...providerMetadata == null ? void 0 : providerMetadata.bedrock
    };
    switch (type) {
      case "regular": {
        const { toolConfig, toolWarnings } = prepareTools(mode);
        return {
          command: {
            ...baseArgs,
            ...((_g = toolConfig.tools) == null ? void 0 : _g.length) ? { toolConfig } : {}
          },
          warnings: [...warnings, ...toolWarnings]
        };
      }
      case "object-json": {
        throw new import_provider4.UnsupportedFunctionalityError({
          functionality: "json-mode object generation"
        });
      }
      case "object-tool": {
        return {
          command: {
            ...baseArgs,
            toolConfig: {
              tools: [
                {
                  toolSpec: {
                    name: mode.tool.name,
                    description: mode.tool.description,
                    inputSchema: {
                      json: mode.tool.parameters
                    }
                  }
                }
              ],
              toolChoice: { tool: { name: mode.tool.name } }
            }
          },
          warnings
        };
      }
      default: {
        const _exhaustiveCheck = type;
        throw new Error(`Unsupported type: ${_exhaustiveCheck}`);
      }
    }
  }
  async doGenerate(options) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p;
    const { command: args, warnings } = this.getArgs(options);
    const url = `${this.getUrl(this.modelId)}/converse`;
    const { value: response, responseHeaders } = await (0, import_provider_utils3.postJsonToApi)({
      url,
      headers: (0, import_provider_utils3.combineHeaders)(
        await (0, import_provider_utils3.resolve)(this.config.headers),
        options.headers
      ),
      body: args,
      failedResponseHandler: (0, import_provider_utils3.createJsonErrorResponseHandler)({
        errorSchema: BedrockErrorSchema,
        errorToMessage: (error) => {
          var _a2;
          return `${(_a2 = error.message) != null ? _a2 : "Unknown error"}`;
        }
      }),
      successfulResponseHandler: (0, import_provider_utils3.createJsonResponseHandler)(
        BedrockResponseSchema
      ),
      abortSignal: options.abortSignal,
      fetch: this.config.fetch
    });
    const { messages: rawPrompt, ...rawSettings } = args;
    const providerMetadata = response.trace || response.usage ? {
      bedrock: {
        ...response.trace && typeof response.trace === "object" ? { trace: response.trace } : {},
        ...response.usage && {
          usage: {
            cacheReadInputTokens: (_b = (_a = response.usage) == null ? void 0 : _a.cacheReadInputTokens) != null ? _b : Number.NaN,
            cacheWriteInputTokens: (_d = (_c = response.usage) == null ? void 0 : _c.cacheWriteInputTokens) != null ? _d : Number.NaN
          }
        }
      }
    } : void 0;
    const reasoning = response.output.message.content.filter((content) => content.reasoningContent).map((content) => {
      var _a2;
      if (content.reasoningContent && "reasoningText" in content.reasoningContent) {
        return {
          type: "text",
          text: content.reasoningContent.reasoningText.text,
          ...content.reasoningContent.reasoningText.signature && {
            signature: content.reasoningContent.reasoningText.signature
          }
        };
      } else if (content.reasoningContent && "redactedReasoning" in content.reasoningContent) {
        return {
          type: "redacted",
          data: (_a2 = content.reasoningContent.redactedReasoning.data) != null ? _a2 : ""
        };
      } else {
        return void 0;
      }
    }).filter((item) => item !== void 0);
    return {
      text: (_h = (_g = (_f = (_e = response.output) == null ? void 0 : _e.message) == null ? void 0 : _f.content) == null ? void 0 : _g.map((part) => {
        var _a2;
        return (_a2 = part.text) != null ? _a2 : "";
      }).join("")) != null ? _h : void 0,
      toolCalls: (_l = (_k = (_j = (_i = response.output) == null ? void 0 : _i.message) == null ? void 0 : _j.content) == null ? void 0 : _k.filter((part) => !!part.toolUse)) == null ? void 0 : _l.map((part) => {
        var _a2, _b2, _c2, _d2, _e2, _f2;
        return {
          toolCallType: "function",
          toolCallId: (_b2 = (_a2 = part.toolUse) == null ? void 0 : _a2.toolUseId) != null ? _b2 : this.config.generateId(),
          toolName: (_d2 = (_c2 = part.toolUse) == null ? void 0 : _c2.name) != null ? _d2 : `tool-${this.config.generateId()}`,
          args: JSON.stringify((_f2 = (_e2 = part.toolUse) == null ? void 0 : _e2.input) != null ? _f2 : "")
        };
      }),
      finishReason: mapBedrockFinishReason(
        response.stopReason
      ),
      usage: {
        promptTokens: (_n = (_m = response.usage) == null ? void 0 : _m.inputTokens) != null ? _n : Number.NaN,
        completionTokens: (_p = (_o = response.usage) == null ? void 0 : _o.outputTokens) != null ? _p : Number.NaN
      },
      rawCall: { rawPrompt, rawSettings },
      rawResponse: { headers: responseHeaders },
      warnings,
      reasoning: reasoning.length > 0 ? reasoning : void 0,
      ...providerMetadata && { providerMetadata }
    };
  }
  async doStream(options) {
    const { command: args, warnings } = this.getArgs(options);
    const url = `${this.getUrl(this.modelId)}/converse-stream`;
    const { value: response, responseHeaders } = await (0, import_provider_utils3.postJsonToApi)({
      url,
      headers: (0, import_provider_utils3.combineHeaders)(
        await (0, import_provider_utils3.resolve)(this.config.headers),
        options.headers
      ),
      body: args,
      failedResponseHandler: (0, import_provider_utils3.createJsonErrorResponseHandler)({
        errorSchema: BedrockErrorSchema,
        errorToMessage: (error) => `${error.type}: ${error.message}`
      }),
      successfulResponseHandler: createBedrockEventStreamResponseHandler(BedrockStreamSchema),
      abortSignal: options.abortSignal,
      fetch: this.config.fetch
    });
    const { messages: rawPrompt, ...rawSettings } = args;
    let finishReason = "unknown";
    let usage = {
      promptTokens: Number.NaN,
      completionTokens: Number.NaN
    };
    let providerMetadata = void 0;
    const toolCallContentBlocks = {};
    return {
      stream: response.pipeThrough(
        new TransformStream({
          transform(chunk, controller) {
            var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n;
            function enqueueError(bedrockError) {
              finishReason = "error";
              controller.enqueue({ type: "error", error: bedrockError });
            }
            if (!chunk.success) {
              enqueueError(chunk.error);
              return;
            }
            const value = chunk.value;
            if (value.internalServerException) {
              enqueueError(value.internalServerException);
              return;
            }
            if (value.modelStreamErrorException) {
              enqueueError(value.modelStreamErrorException);
              return;
            }
            if (value.throttlingException) {
              enqueueError(value.throttlingException);
              return;
            }
            if (value.validationException) {
              enqueueError(value.validationException);
              return;
            }
            if (value.messageStop) {
              finishReason = mapBedrockFinishReason(
                value.messageStop.stopReason
              );
            }
            if (value.metadata) {
              usage = {
                promptTokens: (_b = (_a = value.metadata.usage) == null ? void 0 : _a.inputTokens) != null ? _b : Number.NaN,
                completionTokens: (_d = (_c = value.metadata.usage) == null ? void 0 : _c.outputTokens) != null ? _d : Number.NaN
              };
              const cacheUsage = ((_e = value.metadata.usage) == null ? void 0 : _e.cacheReadInputTokens) != null || ((_f = value.metadata.usage) == null ? void 0 : _f.cacheWriteInputTokens) != null ? {
                usage: {
                  cacheReadInputTokens: (_h = (_g = value.metadata.usage) == null ? void 0 : _g.cacheReadInputTokens) != null ? _h : Number.NaN,
                  cacheWriteInputTokens: (_j = (_i = value.metadata.usage) == null ? void 0 : _i.cacheWriteInputTokens) != null ? _j : Number.NaN
                }
              } : void 0;
              const trace = value.metadata.trace ? {
                trace: value.metadata.trace
              } : void 0;
              if (cacheUsage || trace) {
                providerMetadata = {
                  bedrock: {
                    ...cacheUsage,
                    ...trace
                  }
                };
              }
            }
            if (((_k = value.contentBlockDelta) == null ? void 0 : _k.delta) && "text" in value.contentBlockDelta.delta && value.contentBlockDelta.delta.text) {
              controller.enqueue({
                type: "text-delta",
                textDelta: value.contentBlockDelta.delta.text
              });
            }
            if (((_l = value.contentBlockDelta) == null ? void 0 : _l.delta) && "reasoningContent" in value.contentBlockDelta.delta && value.contentBlockDelta.delta.reasoningContent) {
              const reasoningContent = value.contentBlockDelta.delta.reasoningContent;
              if ("text" in reasoningContent && reasoningContent.text) {
                controller.enqueue({
                  type: "reasoning",
                  textDelta: reasoningContent.text
                });
              } else if ("signature" in reasoningContent && reasoningContent.signature) {
                controller.enqueue({
                  type: "reasoning-signature",
                  signature: reasoningContent.signature
                });
              } else if ("data" in reasoningContent && reasoningContent.data) {
                controller.enqueue({
                  type: "redacted-reasoning",
                  data: reasoningContent.data
                });
              }
            }
            const contentBlockStart = value.contentBlockStart;
            if (((_m = contentBlockStart == null ? void 0 : contentBlockStart.start) == null ? void 0 : _m.toolUse) != null) {
              const toolUse = contentBlockStart.start.toolUse;
              toolCallContentBlocks[contentBlockStart.contentBlockIndex] = {
                toolCallId: toolUse.toolUseId,
                toolName: toolUse.name,
                jsonText: ""
              };
            }
            const contentBlockDelta = value.contentBlockDelta;
            if ((contentBlockDelta == null ? void 0 : contentBlockDelta.delta) && "toolUse" in contentBlockDelta.delta && contentBlockDelta.delta.toolUse) {
              const contentBlock = toolCallContentBlocks[contentBlockDelta.contentBlockIndex];
              const delta = (_n = contentBlockDelta.delta.toolUse.input) != null ? _n : "";
              controller.enqueue({
                type: "tool-call-delta",
                toolCallType: "function",
                toolCallId: contentBlock.toolCallId,
                toolName: contentBlock.toolName,
                argsTextDelta: delta
              });
              contentBlock.jsonText += delta;
            }
            const contentBlockStop = value.contentBlockStop;
            if (contentBlockStop != null) {
              const index = contentBlockStop.contentBlockIndex;
              const contentBlock = toolCallContentBlocks[index];
              if (contentBlock != null) {
                controller.enqueue({
                  type: "tool-call",
                  toolCallType: "function",
                  toolCallId: contentBlock.toolCallId,
                  toolName: contentBlock.toolName,
                  args: contentBlock.jsonText
                });
                delete toolCallContentBlocks[index];
              }
            }
          },
          flush(controller) {
            controller.enqueue({
              type: "finish",
              finishReason,
              usage,
              ...providerMetadata && { providerMetadata }
            });
          }
        })
      ),
      rawCall: { rawPrompt, rawSettings },
      rawResponse: { headers: responseHeaders },
      warnings
    };
  }
  getUrl(modelId) {
    const encodedModelId = encodeURIComponent(modelId);
    return `${this.config.baseUrl()}/model/${encodedModelId}`;
  }
};
var BedrockReasoningConfigOptionsSchema = import_zod2.z.object({
  type: import_zod2.z.union([import_zod2.z.literal("enabled"), import_zod2.z.literal("disabled")]).nullish(),
  budget_tokens: import_zod2.z.number().nullish(),
  budgetTokens: import_zod2.z.number().nullish()
}).nullish();
var BedrockStopReasonSchema = import_zod2.z.union([
  import_zod2.z.enum(BEDROCK_STOP_REASONS),
  import_zod2.z.string()
]);
var BedrockToolUseSchema = import_zod2.z.object({
  toolUseId: import_zod2.z.string(),
  name: import_zod2.z.string(),
  input: import_zod2.z.unknown()
});
var BedrockReasoningTextSchema = import_zod2.z.object({
  signature: import_zod2.z.string().nullish(),
  text: import_zod2.z.string()
});
var BedrockRedactedReasoningSchema = import_zod2.z.object({
  data: import_zod2.z.string()
});
var BedrockResponseSchema = import_zod2.z.object({
  metrics: import_zod2.z.object({
    latencyMs: import_zod2.z.number()
  }).nullish(),
  output: import_zod2.z.object({
    message: import_zod2.z.object({
      content: import_zod2.z.array(
        import_zod2.z.object({
          text: import_zod2.z.string().nullish(),
          toolUse: BedrockToolUseSchema.nullish(),
          reasoningContent: import_zod2.z.union([
            import_zod2.z.object({
              reasoningText: BedrockReasoningTextSchema
            }),
            import_zod2.z.object({
              redactedReasoning: BedrockRedactedReasoningSchema
            })
          ]).nullish()
        })
      ),
      role: import_zod2.z.string()
    })
  }),
  stopReason: BedrockStopReasonSchema,
  trace: import_zod2.z.unknown().nullish(),
  usage: import_zod2.z.object({
    inputTokens: import_zod2.z.number(),
    outputTokens: import_zod2.z.number(),
    totalTokens: import_zod2.z.number(),
    cacheReadInputTokens: import_zod2.z.number().nullish(),
    cacheWriteInputTokens: import_zod2.z.number().nullish()
  })
});
var BedrockStreamSchema = import_zod2.z.object({
  contentBlockDelta: import_zod2.z.object({
    contentBlockIndex: import_zod2.z.number(),
    delta: import_zod2.z.union([
      import_zod2.z.object({ text: import_zod2.z.string() }),
      import_zod2.z.object({ toolUse: import_zod2.z.object({ input: import_zod2.z.string() }) }),
      import_zod2.z.object({
        reasoningContent: import_zod2.z.object({ text: import_zod2.z.string() })
      }),
      import_zod2.z.object({
        reasoningContent: import_zod2.z.object({
          signature: import_zod2.z.string()
        })
      }),
      import_zod2.z.object({
        reasoningContent: import_zod2.z.object({ data: import_zod2.z.string() })
      })
    ]).nullish()
  }).nullish(),
  contentBlockStart: import_zod2.z.object({
    contentBlockIndex: import_zod2.z.number(),
    start: import_zod2.z.object({
      toolUse: BedrockToolUseSchema.nullish()
    }).nullish()
  }).nullish(),
  contentBlockStop: import_zod2.z.object({
    contentBlockIndex: import_zod2.z.number()
  }).nullish(),
  internalServerException: import_zod2.z.record(import_zod2.z.unknown()).nullish(),
  messageStop: import_zod2.z.object({
    additionalModelResponseFields: import_zod2.z.record(import_zod2.z.unknown()).nullish(),
    stopReason: BedrockStopReasonSchema
  }).nullish(),
  metadata: import_zod2.z.object({
    trace: import_zod2.z.unknown().nullish(),
    usage: import_zod2.z.object({
      cacheReadInputTokens: import_zod2.z.number().nullish(),
      cacheWriteInputTokens: import_zod2.z.number().nullish(),
      inputTokens: import_zod2.z.number(),
      outputTokens: import_zod2.z.number()
    }).nullish()
  }).nullish(),
  modelStreamErrorException: import_zod2.z.record(import_zod2.z.unknown()).nullish(),
  throttlingException: import_zod2.z.record(import_zod2.z.unknown()).nullish(),
  validationException: import_zod2.z.record(import_zod2.z.unknown()).nullish()
});

// src/bedrock-embedding-model.ts
var import_provider_utils4 = require("@ai-sdk/provider-utils");
var import_zod3 = require("zod");
var BedrockEmbeddingModel = class {
  constructor(modelId, settings, config) {
    this.modelId = modelId;
    this.settings = settings;
    this.config = config;
    this.specificationVersion = "v1";
    this.provider = "amazon-bedrock";
    this.maxEmbeddingsPerCall = void 0;
    this.supportsParallelCalls = true;
  }
  getUrl(modelId) {
    const encodedModelId = encodeURIComponent(modelId);
    return `${this.config.baseUrl()}/model/${encodedModelId}/invoke`;
  }
  async doEmbed({
    values,
    headers,
    abortSignal
  }) {
    const embedSingleText = async (inputText) => {
      const args = {
        inputText,
        dimensions: this.settings.dimensions,
        normalize: this.settings.normalize
      };
      const url = this.getUrl(this.modelId);
      const { value: response } = await (0, import_provider_utils4.postJsonToApi)({
        url,
        headers: await (0, import_provider_utils4.resolve)(
          (0, import_provider_utils4.combineHeaders)(await (0, import_provider_utils4.resolve)(this.config.headers), headers)
        ),
        body: args,
        failedResponseHandler: (0, import_provider_utils4.createJsonErrorResponseHandler)({
          errorSchema: BedrockErrorSchema,
          errorToMessage: (error) => `${error.type}: ${error.message}`
        }),
        successfulResponseHandler: (0, import_provider_utils4.createJsonResponseHandler)(
          BedrockEmbeddingResponseSchema
        ),
        fetch: this.config.fetch,
        abortSignal
      });
      return {
        embedding: response.embedding,
        inputTextTokenCount: response.inputTextTokenCount
      };
    };
    const responses = await Promise.all(values.map(embedSingleText));
    return responses.reduce(
      (accumulated, response) => {
        accumulated.embeddings.push(response.embedding);
        accumulated.usage.tokens += response.inputTextTokenCount;
        return accumulated;
      },
      { embeddings: [], usage: { tokens: 0 } }
    );
  }
};
var BedrockEmbeddingResponseSchema = import_zod3.z.object({
  embedding: import_zod3.z.array(import_zod3.z.number()),
  inputTextTokenCount: import_zod3.z.number()
});

// src/bedrock-image-model.ts
var import_provider_utils5 = require("@ai-sdk/provider-utils");

// src/bedrock-image-settings.ts
var modelMaxImagesPerCall = {
  "amazon.nova-canvas-v1:0": 5
};

// src/bedrock-image-model.ts
var import_zod4 = require("zod");
var BedrockImageModel = class {
  constructor(modelId, settings, config) {
    this.modelId = modelId;
    this.settings = settings;
    this.config = config;
    this.specificationVersion = "v1";
    this.provider = "amazon-bedrock";
  }
  get maxImagesPerCall() {
    var _a, _b;
    return (_b = (_a = this.settings.maxImagesPerCall) != null ? _a : modelMaxImagesPerCall[this.modelId]) != null ? _b : 1;
  }
  getUrl(modelId) {
    const encodedModelId = encodeURIComponent(modelId);
    return `${this.config.baseUrl()}/model/${encodedModelId}/invoke`;
  }
  async doGenerate({
    prompt,
    n,
    size,
    aspectRatio,
    seed,
    providerOptions,
    headers,
    abortSignal
  }) {
    var _a, _b, _c, _d, _e, _f;
    const warnings = [];
    const [width, height] = size ? size.split("x").map(Number) : [];
    const args = {
      taskType: "TEXT_IMAGE",
      textToImageParams: {
        text: prompt,
        ...((_a = providerOptions == null ? void 0 : providerOptions.bedrock) == null ? void 0 : _a.negativeText) ? {
          negativeText: providerOptions.bedrock.negativeText
        } : {}
      },
      imageGenerationConfig: {
        ...width ? { width } : {},
        ...height ? { height } : {},
        ...seed ? { seed } : {},
        ...n ? { numberOfImages: n } : {},
        ...((_b = providerOptions == null ? void 0 : providerOptions.bedrock) == null ? void 0 : _b.quality) ? { quality: providerOptions.bedrock.quality } : {},
        ...((_c = providerOptions == null ? void 0 : providerOptions.bedrock) == null ? void 0 : _c.cfgScale) ? { cfgScale: providerOptions.bedrock.cfgScale } : {}
      }
    };
    if (aspectRatio != void 0) {
      warnings.push({
        type: "unsupported-setting",
        setting: "aspectRatio",
        details: "This model does not support aspect ratio. Use `size` instead."
      });
    }
    const currentDate = (_f = (_e = (_d = this.config._internal) == null ? void 0 : _d.currentDate) == null ? void 0 : _e.call(_d)) != null ? _f : /* @__PURE__ */ new Date();
    const { value: response, responseHeaders } = await (0, import_provider_utils5.postJsonToApi)({
      url: this.getUrl(this.modelId),
      headers: await (0, import_provider_utils5.resolve)(
        (0, import_provider_utils5.combineHeaders)(await (0, import_provider_utils5.resolve)(this.config.headers), headers)
      ),
      body: args,
      failedResponseHandler: (0, import_provider_utils5.createJsonErrorResponseHandler)({
        errorSchema: BedrockErrorSchema,
        errorToMessage: (error) => `${error.type}: ${error.message}`
      }),
      successfulResponseHandler: (0, import_provider_utils5.createJsonResponseHandler)(
        bedrockImageResponseSchema
      ),
      abortSignal,
      fetch: this.config.fetch
    });
    return {
      images: response.images,
      warnings,
      response: {
        timestamp: currentDate,
        modelId: this.modelId,
        headers: responseHeaders
      }
    };
  }
};
var bedrockImageResponseSchema = import_zod4.z.object({
  images: import_zod4.z.array(import_zod4.z.string())
});

// src/headers-utils.ts
function extractHeaders(headers) {
  let originalHeaders = {};
  if (headers) {
    if (headers instanceof Headers) {
      originalHeaders = convertHeadersToRecord(headers);
    } else if (Array.isArray(headers)) {
      for (const [k, v] of headers) {
        originalHeaders[k.toLowerCase()] = v;
      }
    } else {
      originalHeaders = Object.fromEntries(
        Object.entries(headers).map(([k, v]) => [k.toLowerCase(), v])
      );
    }
  }
  return originalHeaders;
}
function convertHeadersToRecord(headers) {
  const record = {};
  headers.forEach((value, key) => {
    record[key.toLowerCase()] = value;
  });
  return record;
}

// src/bedrock-sigv4-fetch.ts
var import_provider_utils6 = require("@ai-sdk/provider-utils");
var import_aws4fetch = require("aws4fetch");
function createSigV4FetchFunction(getCredentials, fetch = globalThis.fetch) {
  return async (input, init) => {
    var _a;
    if (((_a = init == null ? void 0 : init.method) == null ? void 0 : _a.toUpperCase()) !== "POST" || !(init == null ? void 0 : init.body)) {
      return fetch(input, init);
    }
    const url = typeof input === "string" ? input : input instanceof URL ? input.href : input.url;
    const originalHeaders = extractHeaders(init.headers);
    const body = prepareBodyString(init.body);
    const credentials = await getCredentials();
    const signer = new import_aws4fetch.AwsV4Signer({
      url,
      method: "POST",
      headers: Object.entries((0, import_provider_utils6.removeUndefinedEntries)(originalHeaders)),
      body,
      region: credentials.region,
      accessKeyId: credentials.accessKeyId,
      secretAccessKey: credentials.secretAccessKey,
      sessionToken: credentials.sessionToken,
      service: "bedrock"
    });
    const signingResult = await signer.sign();
    const signedHeaders = convertHeadersToRecord(signingResult.headers);
    return fetch(input, {
      ...init,
      body,
      headers: (0, import_provider_utils6.removeUndefinedEntries)(
        (0, import_provider_utils6.combineHeaders)(originalHeaders, signedHeaders)
      )
    });
  };
}
function prepareBodyString(body) {
  if (typeof body === "string") {
    return body;
  } else if (body instanceof Uint8Array) {
    return new TextDecoder().decode(body);
  } else if (body instanceof ArrayBuffer) {
    return new TextDecoder().decode(new Uint8Array(body));
  } else {
    return JSON.stringify(body);
  }
}

// src/bedrock-provider.ts
function createAmazonBedrock(options = {}) {
  const sigv4Fetch = createSigV4FetchFunction(async () => {
    const region = (0, import_provider_utils7.loadSetting)({
      settingValue: options.region,
      settingName: "region",
      environmentVariableName: "AWS_REGION",
      description: "AWS region"
    });
    if (options.credentialProvider) {
      return {
        ...await options.credentialProvider(),
        region
      };
    }
    return {
      region,
      accessKeyId: (0, import_provider_utils7.loadSetting)({
        settingValue: options.accessKeyId,
        settingName: "accessKeyId",
        environmentVariableName: "AWS_ACCESS_KEY_ID",
        description: "AWS access key ID"
      }),
      secretAccessKey: (0, import_provider_utils7.loadSetting)({
        settingValue: options.secretAccessKey,
        settingName: "secretAccessKey",
        environmentVariableName: "AWS_SECRET_ACCESS_KEY",
        description: "AWS secret access key"
      }),
      sessionToken: (0, import_provider_utils7.loadOptionalSetting)({
        settingValue: options.sessionToken,
        environmentVariableName: "AWS_SESSION_TOKEN"
      })
    };
  }, options.fetch);
  const getBaseUrl = () => {
    var _a, _b;
    return (_b = (0, import_provider_utils7.withoutTrailingSlash)(
      (_a = options.baseURL) != null ? _a : `https://bedrock-runtime.${(0, import_provider_utils7.loadSetting)({
        settingValue: options.region,
        settingName: "region",
        environmentVariableName: "AWS_REGION",
        description: "AWS region"
      })}.amazonaws.com`
    )) != null ? _b : `https://bedrock-runtime.us-east-1.amazonaws.com`;
  };
  const createChatModel = (modelId, settings = {}) => {
    var _a;
    return new BedrockChatLanguageModel(modelId, settings, {
      baseUrl: getBaseUrl,
      headers: (_a = options.headers) != null ? _a : {},
      fetch: sigv4Fetch,
      generateId: import_provider_utils7.generateId
    });
  };
  const provider = function(modelId, settings) {
    if (new.target) {
      throw new Error(
        "The Amazon Bedrock model function cannot be called with the new keyword."
      );
    }
    return createChatModel(modelId, settings);
  };
  const createEmbeddingModel = (modelId, settings = {}) => {
    var _a;
    return new BedrockEmbeddingModel(modelId, settings, {
      baseUrl: getBaseUrl,
      headers: (_a = options.headers) != null ? _a : {},
      fetch: sigv4Fetch
    });
  };
  const createImageModel = (modelId, settings = {}) => {
    var _a;
    return new BedrockImageModel(modelId, settings, {
      baseUrl: getBaseUrl,
      headers: (_a = options.headers) != null ? _a : {},
      fetch: sigv4Fetch
    });
  };
  provider.languageModel = createChatModel;
  provider.embedding = createEmbeddingModel;
  provider.textEmbedding = createEmbeddingModel;
  provider.textEmbeddingModel = createEmbeddingModel;
  provider.image = createImageModel;
  provider.imageModel = createImageModel;
  return provider;
}
var bedrock = createAmazonBedrock();
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  bedrock,
  createAmazonBedrock
});
//# sourceMappingURL=index.js.map