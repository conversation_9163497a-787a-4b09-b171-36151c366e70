# TaskMaster MCP Conflict Analysis

## Problem Statement

When running two TaskMaster MCP instances (Windows Cursor + WSL Claude Code) on the same project directory, both instances conflict over configuration files, causing operational issues.

## Root Cause Analysis

### Current Architecture

Both MCP instances use **project-based configuration detection**:

```javascript
// Both MCPs execute this logic:
findProjectRoot() → searches upward for project markers (.git, package.json, .taskmaster)
getConfig() → loads config from {projectRoot}/.taskmaster/config.json
```

### Project Markers Detection
```javascript
const projectMarkers = [
    '.taskmaster',
    'tasks.json', 
    '.git',
    '.svn',
    'package.json',
    'yarn.lock',
    'package-lock.json'
];
```

### Conflict Scenario

1. **Windows Cursor** opens project at `/mnt/c/Users/<USER>/project/`
2. **WSL Claude Code** accesses same project via Windows mount
3. **Both MCPs detect same project root**: `/mnt/c/Users/<USER>/project/`
4. **Both try to use**: `/mnt/c/Users/<USER>/project/.taskmaster/config.json`
5. **Result**: Configuration conflicts, task state corruption, operational failures

## Configuration Flow Analysis

### Current Config Resolution Logic

```javascript
function _loadAndValidateConfig(explicitRoot = null) {
    let rootToUse = explicitRoot;
    
    if (!rootToUse) {
        rootToUse = findProjectRoot(); // PROBLEM: Both find same root
        if (rootToUse) {
            configSource = `found root (${rootToUse})`;
        } else {
            return defaults;
        }
    }
    
    const configPath = findConfigPath(null, { projectRoot: rootToUse });
    // Both try to read/write same configPath
}
```

### Files in Conflict

1. **Configuration**: `.taskmaster/config.json`
2. **Task State**: `.taskmaster/state.json`
3. **Task Database**: `.taskmaster/tasks/tasks.json`
4. **Reports**: `.taskmaster/reports/task-complexity-report.json`
5. **Cache**: In-memory LRU cache (per-process, but state conflicts)

## Impact Assessment

### Symptoms Observed
- Configuration overwrites between MCP instances
- Task state inconsistencies
- Duplicate or missing tasks
- Cache invalidation issues
- Operation failures

### Affected Components
- **Configuration Manager** (`scripts/modules/config-manager.js`)
- **Path Utilities** (`src/utils/path-utils.js`)
- **Context Manager** (`mcp-server/src/core/context-manager.js`)
- **All MCP Tools** (40+ tools affected)

## Technical Analysis

### Code Points of Conflict

1. **config-manager.js:98** - `findProjectRoot()` call
2. **path-utils.js:53** - Project marker detection
3. **context-manager.js:30** - LRU cache instance creation
4. **All MCP tools** - Path resolution through common utilities

### Current Installation Locations

- **Windows MCP**: `C:\Users\<USER>\Documents\Cline\MCP\task-master-ai\`
- **WSL MCP**: `/home/<USER>/projects/mcp-servers/claude-task-master-main/`

## Solution Requirements

### Must Have
1. **Complete isolation** between Windows and WSL MCP instances
2. **No configuration conflicts** on same project
3. **Backward compatibility** with existing setups
4. **Minimal code changes** for maintainability

### Should Have
1. **Clear separation of concerns** (Windows = installation-based, WSL = project-based)
2. **Environment variable control** for configuration override
3. **Easy debugging** and troubleshooting

## Next Steps

See `windows-mcp-modification-guide.md` for detailed implementation instructions.

## References

- Configuration Manager: `scripts/modules/config-manager.js`
- Path Utilities: `src/utils/path-utils.js`
- MCP Server Entry: `mcp-server/server.js`
- Context Manager: `mcp-server/src/core/context-manager.js`