{"name": "Task Master AI", "version": "0.18.0", "description": "A task management system for ambitious AI-driven development", "projectRoot": ".", "defaultProvider": "openrouter", "providers": {"anthropic": {"enabled": false, "model": "claude-3-5-sonnet-20241022"}, "openai": {"enabled": false, "model": "gpt-4o"}, "openrouter": {"enabled": true, "model": "anthropic/claude-3.5-sonnet"}, "perplexity": {"enabled": true, "model": "llama-3.1-sonar-large-128k-online"}}, "features": {"autoSave": true, "backupOnSave": true, "validateDependencies": true, "complexityAnalysis": true}, "paths": {"tasks": "./tasks", "backups": "./backups", "logs": "./logs"}, "logging": {"level": "info", "file": "./logs/task-master.log"}}