# Windows MCP Modification Guide

## Overview

This guide provides step-by-step instructions to modify the Windows TaskMaster MCP installation to use **installation-based configuration** instead of **project-based configuration**.

## Goal

Make Windows MCP always use its installation directory for configuration storage, preventing conflicts with WSL MCP that uses project-based configuration.

## Installation Details

- **Windows MCP Location**: `C:\Users\<USER>\Documents\Cline\MCP\task-master-ai\`
- **WSL Mount Path**: `/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/`

## Required Modifications

### 1. Modify MCP Server Entry Point

**File**: `C:\Users\<USER>\Documents\Cline\MCP\task-master-ai\mcp-server\server.js`

**Add at the top** (before other imports):

```javascript
#!/usr/bin/env node

import path from 'path';
import { fileURLToPath } from 'url';

// Calculate installation directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Force configuration root to installation directory
process.env.TASKMASTER_CONFIG_ROOT = path.join(__dirname, '..');

import TaskMasterMCPServer from './src/index.js';
import dotenv from 'dotenv';
import logger from './src/logger.js';

// ... rest of existing file remains unchanged
```

### 2. Modify Configuration Manager

**File**: `C:\Users\<USER>\Documents\Cline\MCP\task-master-ai\scripts\modules\config-manager.js`

**Locate function** `_loadAndValidateConfig` (around line 89)

**Replace the section** (lines 96-106):

```javascript
// BEFORE (original code)
if (!rootToUse) {
    rootToUse = findProjectRoot();
    if (rootToUse) {
        configSource = `found root (${rootToUse})`;
    } else {
        // No root found, return defaults immediately
        return defaults;
    }
}

// AFTER (modified code)
if (!rootToUse) {
    // NEW: Check for forced config root first
    const forcedRoot = process.env.TASKMASTER_CONFIG_ROOT;
    if (forcedRoot) {
        rootToUse = path.resolve(forcedRoot);
        configSource = `forced root (${forcedRoot})`;
    } else {
        rootToUse = findProjectRoot();
        if (rootToUse) {
            configSource = `found root (${rootToUse})`;
        } else {
            // No root found, return defaults immediately
            return defaults;
        }
    }
}
```

**Add path import** at the top of the file if not already present:

```javascript
import path from 'path';
```

### 3. Update Cursor MCP Configuration

**File**: `C:\Users\<USER>\.cursor\mcp.json` (or project-specific `.cursor\mcp.json`)

**Update the taskmaster configuration**:

```json
{
  "mcpServers": {
    "taskmaster-windows": {
      "command": "node",
      "args": ["C:\\Users\\<USER>\\Documents\\Cline\\MCP\\task-master-ai\\mcp-server\\server.js"],
      "env": {
        "TASKMASTER_CONFIG_ROOT": "C:\\Users\\<USER>\\Documents\\Cline\\MCP\\task-master-ai",
        "ANTHROPIC_API_KEY": "YOUR_ANTHROPIC_API_KEY_HERE",
        "PERPLEXITY_API_KEY": "YOUR_PERPLEXITY_API_KEY_HERE",
        "OPENAI_API_KEY": "YOUR_OPENAI_KEY_HERE"
      }
    }
  }
}
```

## Configuration Structure After Changes

### Windows MCP (Installation-Based)

**Base Directory**: `C:\Users\<USER>\Documents\Cline\MCP\task-master-ai\`

**Configuration Files**:
- Config: `C:\Users\<USER>\Documents\Cline\MCP\task-master-ai\.taskmaster\config.json`
- Tasks: `C:\Users\<USER>\Documents\Cline\MCP\task-master-ai\.taskmaster\tasks\tasks.json`
- State: `C:\Users\<USER>\Documents\Cline\MCP\task-master-ai\.taskmaster\state.json`
- Reports: `C:\Users\<USER>\Documents\Cline\MCP\task-master-ai\.taskmaster\reports\`

**Behavior**:
- ✅ Never modifies project directories
- ✅ Uses installation-specific configuration
- ✅ Maintains separate task database
- ✅ Independent of project structure

### Sample Windows Config File

**File**: `C:\Users\<USER>\Documents\Cline\MCP\task-master-ai\.taskmaster\config.json`

```json
{
  "models": {
    "main": {
      "provider": "anthropic",
      "modelId": "claude-3-7-sonnet-20250219",
      "maxTokens": 64000,
      "temperature": 0.2
    },
    "research": {
      "provider": "perplexity",
      "modelId": "sonar-pro",
      "maxTokens": 8700,
      "temperature": 0.1
    },
    "fallback": {
      "provider": "anthropic",
      "modelId": "claude-3-5-sonnet",
      "maxTokens": 8192,
      "temperature": 0.2
    }
  },
  "global": {
    "logLevel": "info",
    "debug": false,
    "defaultNumTasks": 10,
    "defaultSubtasks": 5,
    "defaultPriority": "medium",
    "projectName": "Windows TaskMaster",
    "responseLanguage": "English"
  }
}
```

## Implementation Steps

### Step 1: Backup Current Installation

```bash
# From WSL terminal
cp -r /mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai /mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai-backup
```

### Step 2: Apply Code Changes

1. Edit `server.js` as described above
2. Edit `config-manager.js` as described above
3. Update Cursor MCP configuration

### Step 3: Create Initial Configuration Directory

```bash
# From WSL terminal
mkdir -p "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster"
mkdir -p "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster/tasks"
mkdir -p "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster/reports"
```

### Step 4: Restart Cursor

1. Close Cursor completely
2. Reopen Cursor
3. Verify MCP connection in Cursor settings

## Testing Verification

See `testing-verification.md` for detailed testing procedures.

## Troubleshooting

### Issue: MCP Not Loading

**Symptoms**: MCP server not appearing in Cursor
**Solution**: 
1. Check file paths in mcp.json
2. Verify Node.js can execute the server.js file
3. Check Cursor logs for errors

### Issue: Configuration Not Found

**Symptoms**: MCP using default configuration
**Solution**:
1. Verify `TASKMASTER_CONFIG_ROOT` environment variable is set
2. Check that `.taskmaster` directory exists in installation path
3. Manually create config.json if needed

### Issue: Permission Errors

**Symptoms**: Cannot write to configuration directory
**Solution**:
1. Run Cursor as administrator
2. Check folder permissions on installation directory
3. Verify WSL can access mounted Windows drive

## Rollback Procedure

If modifications cause issues:

1. Restore from backup:
   ```bash
   rm -rf /mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai
   mv /mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai-backup /mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai
   ```

2. Revert Cursor mcp.json to original configuration

3. Restart Cursor

## Expected Outcomes

After successful implementation:
- ✅ Windows MCP operates independently of project directories
- ✅ No conflicts with WSL MCP
- ✅ Clean separation of concerns
- ✅ Backward compatibility maintained