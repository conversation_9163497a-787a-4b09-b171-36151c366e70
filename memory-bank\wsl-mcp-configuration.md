# WSL MCP Configuration & Behavior

## Overview

The WSL TaskMaster MCP maintains the **original project-based configuration behavior**, serving as the primary project management system while the Windows MCP operates as an installation-based service.

## Installation Details

- **WSL MCP Location**: `/home/<USER>/projects/mcp-servers/claude-task-master-main/`
- **Type**: Git repository clone
- **Purpose**: Project-focused task management with file system integration

## Configuration Behavior

### Project-Based Configuration

WSL MCP uses the **standard TaskMaster configuration system**:

```javascript
// WSL MCP behavior (unchanged)
findProjectRoot() → searches for project markers
getConfig() → loads from {projectRoot}/.taskmaster/config.json
```

### Project Root Detection

WSL MCP detects project root by searching upward for:

```javascript
const projectMarkers = [
    '.taskmaster',      // TaskMaster project directory
    'tasks.json',       // Legacy TaskMaster file
    '.git',            // Git repository
    '.svn',            // SVN repository
    'package.json',    // Node.js project
    'yarn.lock',       // Yarn project
    'package-lock.json' // NPM project
];
```

## Configuration Structure

### Project-Based Files

For any project at `/path/to/project/`, WSL MCP creates:

```
/path/to/project/
├── .taskmaster/
│   ├── config.json          # Project-specific configuration
│   ├── state.json           # Project state tracking
│   ├── tasks/
│   │   └── tasks.json       # Project task database
│   ├── docs/
│   │   └── prd.txt          # Project requirements document
│   ├── reports/
│   │   └── task-complexity-report.json
│   └── templates/
│       └── example_prd.txt
```

### Sample WSL Configuration

**File**: `{project}/.taskmaster/config.json`

```json
{
  "models": {
    "main": {
      "provider": "claude-code",
      "modelId": "claude-code/sonnet",
      "maxTokens": 64000,
      "temperature": 0.2
    },
    "research": {
      "provider": "perplexity",
      "modelId": "sonar-pro", 
      "maxTokens": 8700,
      "temperature": 0.1
    },
    "fallback": {
      "provider": "anthropic",
      "modelId": "claude-3-5-sonnet",
      "maxTokens": 8192,
      "temperature": 0.2
    }
  },
  "global": {
    "logLevel": "info",
    "debug": true,
    "defaultNumTasks": 15,
    "defaultSubtasks": 7,
    "defaultPriority": "high",
    "projectName": "WSL Project TaskMaster",
    "responseLanguage": "English"
  },
  "claudeCode": {
    "maxTurns": 10,
    "permissionMode": "acceptEdits",
    "allowedTools": ["read", "write", "edit", "bash", "grep"]
  }
}
```

## Claude Code Integration

### MCP Configuration for Claude Code

**Location**: Project-specific or global Claude Code settings

```json
{
  "mcpServers": {
    "taskmaster-wsl": {
      "command": "node",
      "args": ["/home/<USER>/projects/mcp-servers/claude-task-master-main/mcp-server/server.js"],
      "env": {
        "ANTHROPIC_API_KEY": "YOUR_WSL_ANTHROPIC_KEY",
        "PERPLEXITY_API_KEY": "YOUR_WSL_PERPLEXITY_KEY",
        "CLAUDE_CODE_API_KEY": ""
      }
    }
  }
}
```

### Claude Code Provider Settings

WSL MCP is optimized for Claude Code integration:

```json
{
  "models": {
    "main": {
      "provider": "claude-code",
      "modelId": "claude-code/sonnet"
    }
  },
  "claudeCode": {
    "maxTurns": 10,
    "customSystemPrompt": "You are a development assistant integrated with TaskMaster.",
    "permissionMode": "acceptEdits",
    "allowedTools": ["read", "write", "edit", "bash", "grep", "glob"],
    "mcpServers": {
      "taskmaster-wsl": {
        "type": "stdio",
        "command": "node",
        "args": ["/home/<USER>/projects/mcp-servers/claude-task-master-main/mcp-server/server.js"]
      }
    }
  }
}
```

## Operational Behavior

### Project Discovery

1. **Automatic Detection**: WSL MCP automatically finds project root when operating in any project directory
2. **Configuration Loading**: Loads project-specific settings from `.taskmaster/config.json`
3. **Task Management**: Manages tasks within project context
4. **File Integration**: Direct file system access for project files

### Task Management Features

```javascript
// WSL MCP provides full project integration:
- Parse PRD files from project
- Generate task files in project structure  
- Research with project context
- Analyze project complexity
- Integrate with project git history
- Generate project-specific reports
```

### Context Awareness

WSL MCP maintains full project context:

- **Working Directory**: Always relative to project root
- **File Paths**: Resolved relative to project structure
- **Git Integration**: Aware of version control status
- **Dependency Analysis**: Understands project dependencies
- **Code Analysis**: Can analyze project codebase

## Usage Scenarios

### Primary Use Cases

1. **Project Development**: Main task management for active development
2. **Code Analysis**: Deep integration with project codebase
3. **Documentation**: Generate and maintain project documentation
4. **Planning**: Parse PRDs and create development plans
5. **Progress Tracking**: Monitor project task completion

### Integration Points

- **Claude Code CLI**: Direct integration with Claude Code commands
- **Git Workflows**: Task management integrated with git operations
- **File System**: Direct read/write access to project files
- **Development Tools**: Integration with project build systems

## Expected Behavior

### Normal Operations

- ✅ **Project-Focused**: Always operates within project context
- ✅ **File Integration**: Direct project file access and modification
- ✅ **Context Awareness**: Understands project structure and goals
- ✅ **Development Flow**: Integrated with development workflows

### Isolation from Windows MCP

- ✅ **Separate Configuration**: Uses project-based config, not installation config
- ✅ **Independent Tasks**: Maintains separate task database per project
- ✅ **No Conflicts**: Operates independently of Windows MCP
- ✅ **Clean Separation**: No shared state with Windows MCP

## Configuration Examples

### Development Project Setup

```bash
# Initialize TaskMaster in project
cd /path/to/my/project
claude-code "Initialize taskmaster-ai in my project"

# This creates:
# .taskmaster/config.json - Project config
# .taskmaster/tasks/tasks.json - Project tasks
# .taskmaster/docs/prd.txt - Project requirements
```

### Multi-Model Configuration

```json
{
  "models": {
    "main": {
      "provider": "claude-code",
      "modelId": "claude-code/sonnet",
      "maxTokens": 64000,
      "temperature": 0.2
    },
    "research": {
      "provider": "perplexity", 
      "modelId": "sonar-pro",
      "maxTokens": 8700,
      "temperature": 0.1
    },
    "fallback": {
      "provider": "anthropic",
      "modelId": "claude-3-5-sonnet", 
      "maxTokens": 8192,
      "temperature": 0.2
    }
  }
}
```

## Advantages of Project-Based Approach

1. **Context Preservation**: Maintains project-specific task context
2. **File Integration**: Direct access to project files and structure
3. **Team Collaboration**: Shareable project configurations
4. **Version Control**: Configuration can be committed to project repository
5. **Development Flow**: Integrated with standard development workflows

## Maintenance

WSL MCP requires **no modifications** for the dual-MCP setup. It continues to operate normally with:

- Standard project-based configuration
- Full Claude Code integration
- Complete project file access
- Normal TaskMaster functionality

The only requirement is ensuring it doesn't conflict with the Windows MCP, which is achieved through the Windows MCP modifications described in `windows-mcp-modification-guide.md`.