# Implementation Guide: Dual MCP Setup

## Executive Summary

This guide provides a complete implementation strategy for running two TaskMaster MCP instances simultaneously without conflicts:

- **Windows MCP**: Installation-based configuration (modified)
- **WSL MCP**: Project-based configuration (unchanged)

## Architecture Overview

### Before Implementation (Problematic)

```
┌─────────────────┐    ┌─────────────────┐
│   Windows MCP   │    │    WSL MCP      │
│    (Cursor)     │    │ (<PERSON> Code)   │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────┬─────────────────┘
                 │
                 ▼
        ┌─────────────────┐
        │   PROJECT DIR   │
        │ .taskmaster/    │ ← CONFLICT!
        │ ├── config.json │
        │ ├── tasks.json  │
        │ └── state.json  │
        └─────────────────┘
```

### After Implementation (Isolated)

```
┌─────────────────┐              ┌─────────────────┐
│   Windows MCP   │              │    WSL MCP      │
│    (Cursor)     │              │ (Claude Code)   │
└─────────┬───────┘              └─────────┬───────┘
          │                                │
          ▼                                ▼
┌─────────────────┐              ┌─────────────────┐
│ INSTALLATION    │              │   PROJECT DIR   │
│ C:\...\MCP\     │              │ .taskmaster/    │
│ ├── config.json │              │ ├── config.json │
│ ├── tasks.json  │              │ ├── tasks.json  │
│ └── state.json  │              │ └── state.json  │
└─────────────────┘              └─────────────────┘
     ✅ Isolated                      ✅ Isolated
```

## Implementation Strategy

### Phase 1: Preparation & Backup

#### 1.1 Create Backup

```bash
# From WSL terminal
cp -r /mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai \
      /mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai-backup-$(date +%Y%m%d)
```

#### 1.2 Document Current State

```bash
# Check current Windows MCP configuration
ls -la /mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/

# Check current WSL MCP configuration  
ls -la /home/<USER>/projects/mcp-servers/claude-task-master-main/

# Check active MCP processes
ps aux | grep -i task-master
```

### Phase 2: Windows MCP Modifications

#### 2.1 Modify Server Entry Point

**File**: `/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/mcp-server/server.js`

```javascript
#!/usr/bin/env node

import path from 'path';
import { fileURLToPath } from 'url';

// Calculate installation directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Force configuration root to installation directory
process.env.TASKMASTER_CONFIG_ROOT = path.join(__dirname, '..');

import TaskMasterMCPServer from './src/index.js';
import dotenv from 'dotenv';
import logger from './src/logger.js';

// ... rest of file unchanged
```

#### 2.2 Modify Configuration Manager

**File**: `/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/scripts/modules/config-manager.js`

**Add import** (if not present):
```javascript
import path from 'path';
```

**Replace section around line 97**:
```javascript
// BEFORE
if (!rootToUse) {
    rootToUse = findProjectRoot();
    if (rootToUse) {
        configSource = `found root (${rootToUse})`;
    } else {
        return defaults;
    }
}

// AFTER  
if (!rootToUse) {
    // Check for forced config root first
    const forcedRoot = process.env.TASKMASTER_CONFIG_ROOT;
    if (forcedRoot) {
        rootToUse = path.resolve(forcedRoot);
        configSource = `forced root (${forcedRoot})`;
    } else {
        rootToUse = findProjectRoot();
        if (rootToUse) {
            configSource = `found root (${rootToUse})`;
        } else {
            return defaults;
        }
    }
}
```

#### 2.3 Create Installation Configuration Directory

```bash
# Create configuration structure
mkdir -p "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster"
mkdir -p "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster/tasks" 
mkdir -p "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster/reports"
mkdir -p "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster/docs"
mkdir -p "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster/templates"
```

#### 2.4 Create Initial Configuration

**File**: `/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster/config.json`

```json
{
  "models": {
    "main": {
      "provider": "anthropic",
      "modelId": "claude-3-7-sonnet-20250219", 
      "maxTokens": 64000,
      "temperature": 0.2
    },
    "research": {
      "provider": "perplexity",
      "modelId": "sonar-pro",
      "maxTokens": 8700,
      "temperature": 0.1
    },
    "fallback": {
      "provider": "anthropic",
      "modelId": "claude-3-5-sonnet",
      "maxTokens": 8192,
      "temperature": 0.2
    }
  },
  "global": {
    "logLevel": "info",
    "debug": false,
    "defaultNumTasks": 10,
    "defaultSubtasks": 5,
    "defaultPriority": "medium",
    "projectName": "Windows TaskMaster",
    "responseLanguage": "English"
  }
}
```

### Phase 3: Update Cursor Configuration

#### 3.1 Modify Cursor MCP Settings

**File**: `C:\Users\<USER>\.cursor\mcp.json`

```json
{
  "mcpServers": {
    "taskmaster-windows": {
      "command": "node",
      "args": ["C:\\Users\\<USER>\\Documents\\Cline\\MCP\\task-master-ai\\mcp-server\\server.js"],
      "env": {
        "TASKMASTER_CONFIG_ROOT": "C:\\Users\\<USER>\\Documents\\Cline\\MCP\\task-master-ai",
        "ANTHROPIC_API_KEY": "YOUR_ANTHROPIC_API_KEY_HERE",
        "PERPLEXITY_API_KEY": "YOUR_PERPLEXITY_API_KEY_HERE",
        "OPENAI_API_KEY": "YOUR_OPENAI_KEY_HERE"
      }
    }
  }
}
```

### Phase 4: WSL MCP Verification

#### 4.1 Verify WSL MCP Unchanged

```bash
# Confirm WSL MCP uses standard behavior
cd /home/<USER>/projects/mcp-servers/claude-task-master-main/
grep -n "findProjectRoot" scripts/modules/config-manager.js
```

#### 4.2 Verify Claude Code Configuration

Ensure Claude Code MCP configuration points to WSL installation:

```json
{
  "mcpServers": {
    "taskmaster-wsl": {
      "command": "node", 
      "args": ["/home/<USER>/projects/mcp-servers/claude-task-master-main/mcp-server/server.js"],
      "env": {
        "ANTHROPIC_API_KEY": "YOUR_WSL_ANTHROPIC_KEY"
      }
    }
  }
}
```

### Phase 5: Testing & Verification

#### 5.1 Restart Services

```bash
# Kill any running TaskMaster processes
pkill -f task-master

# Restart Cursor (close completely and reopen)
# Restart Claude Code if needed
```

#### 5.2 Verify Isolation

**Test Windows MCP**:
```
1. Open Cursor
2. Open any project directory
3. Use TaskMaster MCP: "Show me the current configuration"
4. Verify it shows Windows installation path
```

**Test WSL MCP**:
```
1. Open Claude Code from WSL terminal in a project
2. Use TaskMaster MCP: "Initialize taskmaster-ai in my project"  
3. Verify it creates .taskmaster/ in project directory
```

#### 5.3 Verify No Conflicts

```bash
# Test both MCPs on same project simultaneously
# 1. Open project in Cursor (Windows MCP active)
# 2. cd to same project in WSL terminal
# 3. Launch Claude Code (WSL MCP active)
# 4. Use TaskMaster commands in both
# 5. Verify no configuration conflicts
```

## Expected Outcomes

### Successful Implementation Results

#### Windows MCP Behavior
- ✅ Uses installation directory for all configuration
- ✅ Never modifies project directories
- ✅ Operates independently of project structure
- ✅ Maintains separate task database
- ✅ Configuration location: `C:\Users\<USER>\Documents\Cline\MCP\task-master-ai\.taskmaster\`

#### WSL MCP Behavior
- ✅ Uses project-based configuration (unchanged)
- ✅ Creates `.taskmaster/` in project directories
- ✅ Integrates with project development workflow
- ✅ Maintains project-specific task context
- ✅ Full Claude Code integration

#### Isolation Verification
- ✅ Both MCPs can operate on same project simultaneously
- ✅ No configuration file conflicts
- ✅ Independent task management
- ✅ Separate API key usage
- ✅ No cross-contamination of settings

## Rollback Plan

If implementation fails:

```bash
# Step 1: Restore Windows MCP from backup
rm -rf /mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai
mv /mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai-backup-* \
   /mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai

# Step 2: Restore original Cursor configuration
# Revert mcp.json to use original npx command

# Step 3: Restart Cursor
```

## Maintenance & Monitoring

### Health Checks

```bash
# Weekly verification script
#!/bin/bash
echo "=== TaskMaster MCP Health Check ==="

echo "Windows MCP Config Location:"
ls -la "/mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster/"

echo "Sample Project WSL MCP Config:"
find /home/<USER>/projects -name ".taskmaster" -type d 2>/dev/null | head -3

echo "Active TaskMaster Processes:"
ps aux | grep -i task-master | grep -v grep
```

### Performance Monitoring

- Monitor memory usage of both MCP processes
- Check for configuration file access conflicts
- Verify API key usage separation
- Monitor task database growth

### Update Strategy

When updating TaskMaster:

1. **Windows MCP**: Update installation and reapply modifications
2. **WSL MCP**: Standard git pull and npm install
3. **Test isolation** after each update
4. **Verify modifications** are preserved

## Success Metrics

### Quantitative Metrics
- ✅ Zero configuration conflicts
- ✅ Both MCPs operational simultaneously 
- ✅ Independent task databases
- ✅ Separate API key usage tracking

### Qualitative Metrics
- ✅ Smooth development workflow
- ✅ Clear separation of concerns
- ✅ Easy troubleshooting
- ✅ Maintainable architecture

## Support & Troubleshooting

### Common Issues

1. **Windows MCP not loading**: Check file paths and permissions
2. **Configuration not found**: Verify TASKMASTER_CONFIG_ROOT variable
3. **WSL MCP conflicts**: Ensure Windows modifications are correct
4. **Performance issues**: Monitor process memory usage

### Debug Commands

```bash
# Check environment variables
env | grep TASKMASTER

# Verify file access
ls -la /mnt/c/Users/<USER>/Documents/Cline/MCP/task-master-ai/.taskmaster/

# Check process details
ps aux | grep task-master | grep -v grep
```

This implementation provides a robust, maintainable solution for dual TaskMaster MCP operation with complete isolation and no conflicts.